[project]
name = "feed"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "numpy",
    "openai",
    "pandas",
    "sqlalchemy",
    "python-dotenv",
    "psycopg2-binary",
    "catalog-processor-bot",
    "dotenv>=0.9.9",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/feed"]

[tool.uv.workspace]
members = ["packages/*"]

[tool.uv.sources]
lib = { workspace = true }
catalog-processor-bot = {workspace = true}
