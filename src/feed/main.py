import typing

GENERAL_FILTERS = {}

CATEGORIES_ALL = object()

def process_catalog(logger_root, categories: typing.Union[object, list[str]] = CATEGORIES_ALL, config_target = None, export = None, category_type = None):
    import os
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from catalog_processor_bot.xml_loader import get_dataframe_from_url
    from catalog_processor_bot.categories_table import CatalogCategory
    from pathlib import Path
    from tempfile import TemporaryDirectory

    CONNECTION_STRING = os.environ.get(f"CONNECTION_STRING_{os.getenv('ENV', 'dev')}")
    config_target = config_target or os.environ.get("CONFIG_TARGET", None)
    if not CONNECTION_STRING:
        raise Exception("Database connection string is not set.")

    engine = create_engine(CONNECTION_STRING)
    Session = sessionmaker(bind=engine)
    session = Session()

    logger_root.info("Gathering categories....")
    categories_from_db = session.query(CatalogCategory).all()

    with TemporaryDirectory() as scratch_dir:
        scratch_dir = Path(scratch_dir)

        logger_root.info("Getting catalog....")
        xml_url = "https://feeds.mergado.com/sportobchod-cz-google-nakupy-cz-2-158506a20c1215dd7081a772d2b1eb92.xml"
        complete_df = get_dataframe_from_url(logger=logger_root, scratch_dir=scratch_dir, xml_url=xml_url)

        if export:
            complete_df.to_csv(export)
            return {"statusCode": 200, "body": "Success"}

        for category_obj in categories_from_db:

            if categories != CATEGORIES_ALL and category_obj.name not in categories:
                continue

            if category_obj.
            process_category(CONNECTION_STRING, category_obj.name, category_obj.product_types, complete_df, logger_root, config_target, category_type)

    return {"statusCode": 200, "body": "Success"}


def process_category(connection_string, domain_name, product_types, complete_df, logger, config_target, category_type):
    import importlib, json, os
    from catalog_processor_bot.save_to_products_db import (save_main_table,
                                                           generate_and_save_embeddings,
                                                           create_bm25_index, create_json_category)
    from catalog_processor_bot.initial_preprocessing import preprocess_catalog
    from catalog_processor_bot.post_processing import clean_column_of_spaces, convert_numeric_columns_to_int_floats
    from catalog_processor_bot.descriptions_processor import update_descriptions_table
    from catalog_processor_bot.get_table_name import get_table_name
    from catalog_processor_bot.save_to_products_db import update_config_yaml

    logger = logger.bind(domain=domain_name)
    if product_types is None and domain_name not in GENERAL_FILTERS:
        logger.warning(f"Skipping category '{domain_name}' as product_types is not set.")
        return

    safe_domain_name = get_table_name(domain_name)
    # Get the custom processing code for a domain_name
    full_module_path = f"catalog_processor_bot.custom_products_transformations.{safe_domain_name}"
    try:
        imported_module = importlib.import_module(full_module_path)
        custom_category_processor = imported_module.process
    except ModuleNotFoundError:
        logger.error(f"Could not find module for {domain_name} - {full_module_path}. Using noop")
        custom_category_processor = lambda x: x

    # Get the product_tpes column values for a given domain_name
    product_types = json.loads(product_types) if product_types is not None else None

    # Run preprocessing tasks for the domain_name dataframe
    df = preprocess_catalog(
        safe_domain_name=safe_domain_name,
        product_types=product_types,
        dataframe=complete_df,
        general_filter=GENERAL_FILTERS.get(domain_name, None),
        logger=logger,
    ).copy()

    if df.empty:
        logger.warning(f"No products for category '{domain_name}' after initial preprocessing.")
        return

    df = custom_category_processor(df)
    df = clean_column_of_spaces(df)
    df = convert_numeric_columns_to_int_floats(df)

    if category_type == 'table':
        update_descriptions_table(logger=logger, df=df, connection_string=connection_string, domain_name=domain_name)
        save_main_table(df=df, table_name=domain_name, connection_string=connection_string, logger=logger)
        generate_and_save_embeddings(df=df, connection_string=connection_string, domain_name=domain_name, logger=logger)

        if "description" in df.columns and not df["description"].isnull().all():
            create_bm25_index(connection_string=connection_string, index_name=domain_name, logger=logger)

        logger.info(f"Finished processing category: {domain_name}")

        if config_target:
            update_config_yaml(config_target, domain_name, df, get_table_name(domain_name))

    elif category_type == 'json':
        create_json_category(df, connection_string, domain_name, logger=logger)


def handler(event, context):
    from feed.utils.logger import logger
    logger_root = logger

    try:
        return process_catalog(logger_root)
    except Exception as e:
        logger_root.exception(e)
        raise


def main():
    import dotenv
    from feed.utils.logger import logger
    logger_root = logger
    import argparse


    dotenv.load_dotenv()

    parser = argparse.ArgumentParser()
    parser.add_argument("--domain", "-d", action='append')
    parser.add_argument("--config", "-c")
    parser.add_argument("--export", "-e", help="Export the whole catalog to a csv file at this path")
    parser.add_argument("--category-type", dest="category_type", default='table', choices=['table', 'json'], help="Category type: 'table' or 'json' (default: table)")
    args = parser.parse_args()

    process_catalog(logger_root, args.domain, config_target=args.config, export=args.export, category_type=args.category_type)


if __name__ == "__main__":
    main()
