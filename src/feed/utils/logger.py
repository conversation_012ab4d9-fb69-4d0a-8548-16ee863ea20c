import json
import os
import sys
import traceback
import warnings

from loguru import logger

LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

LOG_FORMAT_DB = os.getenv("LOG_FORMAT_DB", "{time:YYYY-MM-DD_HH:mm:ss.SSS}|{level}|{name}:{line}|{function}|{extra}|{message}")
LOG_FORMAT_STDOUT = os.getenv("LOG_FORMAT_STDOUT", "{time:YYYY-MM-DD_HH:mm:ss.SSS}|{level}|{name}:{line}|{function}|{message}")

logger.remove(0)  # remove the default logger handler to stderr


# Redirect warnings to loguru logger
def showwarning(message, category, filename, lineno, file=None, line=None):
    new_message = warnings.formatwarning(message, category, filename, lineno, line)
    logger.warning(new_message)


warnings.showwarning = showwarning

if LOG_FORMAT_STDOUT == "JSON":

    def json_format(d: dict) -> str:
        d = d.copy()
        d["timestamp"] = d["time"].isoformat()
        del d["time"]
        d["level"] = d["level"].name
        file = d["file"]
        d["file"] = file.name
        d["filepath"] = file.path
        del d["process"]
        del d["elapsed"]
        del d["thread"]

        if "exception" in d and d["exception"] is None:
            del d["exception"]

        elif "exception" in d and d["exception"] is not None:
            exc_type, exc_value, exc_traceback = d["exception"]
            d["exception"] = {
                "type": exc_type.__name__ if hasattr(exc_type, "__name__") else str(exc_type),
                "value": str(exc_value),
                "traceback": "".join(traceback.format_exception(exc_type, exc_value, exc_traceback)),
            }
            print(d["exception"]["traceback"], file=sys.stderr)

        return json.dumps(d, default=str)

    def sink(message):
        serialized = json_format(message.record)
        print(serialized)

    logger.add(sink, level=LOG_LEVEL)
else:
    logger.add(sys.stdout, level=LOG_LEVEL, colorize=False, format=LOG_FORMAT_STDOUT)
