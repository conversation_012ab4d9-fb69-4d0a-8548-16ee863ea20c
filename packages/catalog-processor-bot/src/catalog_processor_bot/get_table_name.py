import unicodedata


def get_table_name(domain_name: str):
    """
    Convert a domain name to a valid SQL table name.
    Replace spaces and special characters with underscores to create a valid Python module name.
    Remove diacritics from czech language.
    """
    domain_name = "".join(c for c in unicodedata.normalize("NFD", domain_name) if unicodedata.category(c) != "Mn")
    return domain_name.replace(" ", "_").replace(">", "").replace("<", "").replace(",", "")
