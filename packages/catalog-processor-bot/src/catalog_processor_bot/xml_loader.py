import csv
import pathlib
import xml.etree.ElementTree as ET
from os import PathLike
from typing import List
import pandas as pd

import urllib.request

import humanize

import diskcache
import platformdirs


cache = diskcache.Cache(str(pathlib.Path(platformdirs.PlatformDirs("feed", "robosales", ensure_exists=True).user_cache_dir) / "download-cache"))

@cache.memoize(expire=3600)
def download_cached(url) -> bytes:
    with urllib.request.urlopen(url) as fd_url:
        return fd_url.read()

def load_xml_from_url(scratch_dir, xml_url, logger):
    destination = pathlib.Path(scratch_dir) / "source.xml"

    with open(destination, mode="wb") as fd_xml:
        content = download_cached(xml_url)
        fd_xml.write(content)
        logger.info(f"Saved {humanize.naturalsize(len(content))} to {destination}")

    return destination


def load_and_process_xml(xml_file: PathLike) -> pd.DataFrame:
    """
    Load and process XML file into a pandas DataFrame.

    Args:
        xml_file (str or Path): Path to the XML file

    Returns:
        pandas.DataFrame: Processed DataFrame
    """
    # Parse XML file
    tree = ET.parse(xml_file)
    root = tree.getroot()

    # Initialize list to store data
    data = []

    # Iterate through all <item> elements
    for item in root.findall(".//item"):
        item_data = {}
        params = []
        for child in item:
            if child.tag == "PARAM":
                param_name = child.find("PARAM_NAME").text
                param_value = child.find("VAL").text if child.find("VAL") is not None else None
                item_data[param_name] = param_value
                params.append(param_name)
            else:
                item_data[child.tag] = child.text
        data.append(item_data)

    # Create DataFrame
    df = pd.DataFrame(data)
    pd.set_option("display.max_colwidth", None)

    # Rename common columns only if they exist
    columns_to_rename = {
        "{http://base.google.com/ns/1.0}image_link": "image_link",
        "{http://base.google.com/ns/1.0}price": "price_czk",
        "{http://base.google.com/ns/1.0}availability": "availability",
        "{http://base.google.com/ns/1.0}brand": "brand",
        "{http://base.google.com/ns/1.0}color": "color",
        "{http://base.google.com/ns/1.0}material": "material",
        "{http://base.google.com/ns/1.0}product_type": "product_type",
        "{http://base.google.com/ns/1.0}item_group_id": "item_group_id",
    }

    # Filter to only include columns that exist in the dataframe
    existing_columns = {col: new_col for col, new_col in columns_to_rename.items() if col in df.columns}
    df.rename(columns=existing_columns, inplace=True)

    # Columns to drop
    columns_to_drop = [
        "{http://base.google.com/ns/1.0}gtin",
        "{http://base.google.com/ns/1.0}identifier_exists",
        "stock",
        "rozmery_baleni",
        "rozmery_produktu",
        "{http://base.google.com/ns/1.0}custom_label_4",
        "{http://base.google.com/ns/1.0}custom_label_1",
        "{http://base.google.com/ns/1.0}custom_label_2",
        "{http://base.google.com/ns/1.0}custom_label_3",
        "delivery_date",
        "product_length",
        "product_width",
        "product_height",
        "shipping_length",
        "shipping_width",
        "shipping_height",
        "shipping_weight",
        "{http://base.google.com/ns/1.0}ads_grouping",
        "{http://base.google.com/ns/1.0}sale_price",
        "{http://base.google.com/ns/1.0}additional_image_link",
        "customs_number",
    ]

    df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

    return df


def get_unique_product_types(df: pd.DataFrame) -> List[str]:
    """Extract unique product types from the DataFrame.

    Args:
        df: DataFrame containing the product data

    Returns:
        List of unique product types, sorted alphabetically
    """
    if "product_type" not in df.columns:
        return []
    return sorted(df["product_type"].dropna().unique().tolist())


def save_product_types_to_csv(product_types: List[str], output_path: pathlib.Path) -> None:
    """Save product types to a CSV file.

    Args:
        product_types: List of product types to save
        output_path: Path where to save the CSV file
    """
    output_path = output_path.with_suffix(".csv")  # Ensure .csv extension
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Write to CSV file with one product type per line
    with open(output_path, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(["product_type"])  # Write header
        for product_type in sorted(product_types):
            writer.writerow([product_type])
    print(f"Product types saved to: {output_path}")


def main():
    """Main function to execute the XML loading and processing.

    This function will only run if the product_types.csv file doesn't exist or is empty.
    """
    # Define input and output paths
    data_dir = pathlib.Path(__file__).parent.parent.parent / "data"
    markdown_path = data_dir / "product_types.csv"

    # Check if product_types.csv exists and is not empty
    if markdown_path.exists() and markdown_path.read_text().strip():
        print(f"{markdown_path} already exists and is not empty. Skipping processing.")
        return

    # Process the XML file
    xml_url = "https://feeds.mergado.com/sportobchod-cz-google-nakupy-cz-2-158506a20c1215dd7081a772d2b1eb92.xml"
    temp_xml_file = load_xml_from_url(xml_url)
    print(f"Processing XML file: {temp_xml_file}")
    df = load_and_process_xml(temp_xml_file)

    # Save the results to CSV
    data_dir.mkdir(parents=True, exist_ok=True)
    csv_path = data_dir / "catalog_all_products.csv"
    df.to_csv(csv_path, index=False)
    print(f"Data successfully saved to: {csv_path}")

    # Extract and save product types
    product_types = get_unique_product_types(df)
    if product_types:
        save_product_types_to_csv(product_types, markdown_path)

@cache.memoize(ignore={"logger", "scratch_dir"})
def get_dataframe_from_url(*, logger, scratch_dir, xml_url):
    temp_xml_file = load_xml_from_url(
        xml_url=xml_url,
        scratch_dir=scratch_dir,
        logger=logger,
    )
    logger.info("Processing catalog...")
    complete_df = load_and_process_xml(temp_xml_file)
    return complete_df

if __name__ == "__main__":
    main()
