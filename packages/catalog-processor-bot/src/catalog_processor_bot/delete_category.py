#!/usr/bin/env python3
import argparse
import os
import sys

from sqlalchemy import create_engine, text

from catalog_processor_bot.get_table_name import get_table_name

# Constants for table names
DESCRIPTIONS_TABLE_NAME = "product_descriptions"
EMBEDDINGS_TABLE_NAME = "embeddings_for_npf"


def delete_category_data(domain_name: str, connection_string: str) -> None:
    """
    Delete data related to a specific domain from the database.

    Args:
        domain_name: Domain name to delete data for
        connection_string: Database connection string
    """
    engine = create_engine(connection_string)

    try:
        with engine.connect() as conn:
            # Start a transaction
            with conn.begin():
                # 1. Delete the product table
                product_table = f"products_{get_table_name(domain_name)}"
                print(f"Deleting table {product_table}...")

                # Check if the table exists before attempting to drop it
                conn.execute(
                    text(
                        f"""
                    DROP TABLE IF EXISTS {product_table}
                """
                    )
                )
                print(f"Table {product_table} deleted successfully.")

                # 2. Delete records from product_descriptions where domain_name matches
                if domain_name:
                    print(f"Deleting records from {DESCRIPTIONS_TABLE_NAME} where domain_name = '{domain_name}'...")
                    result = conn.execute(
                        text(f"DELETE FROM {DESCRIPTIONS_TABLE_NAME} WHERE domain_name = :domain_name"), {"domain_name": domain_name}
                    )
                    print(f"Deleted {result.rowcount} records from {DESCRIPTIONS_TABLE_NAME}.")

                # 3. Delete records from embeddings_for_npf where domain_name matches
                if domain_name:
                    print(f"Deleting records from {EMBEDDINGS_TABLE_NAME} where domain_name = '{domain_name}'...")
                    result = conn.execute(
                        text(f"DELETE FROM {EMBEDDINGS_TABLE_NAME} WHERE domain_name = :domain_name"), {"domain_name": domain_name}
                    )
                    print(f"Deleted {result.rowcount} records from {EMBEDDINGS_TABLE_NAME}.")

            print("All data deleted successfully.")
    except Exception as e:
        print(f"Error deleting data: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


def main():

    import dotenv
    dotenv.load_dotenv()

    parser = argparse.ArgumentParser(description="Delete category data from the database")
    parser.add_argument("--domain-name", dest="domain_name", default="", help="Domain name to associate with the embeddings")

    args = parser.parse_args()

    # Load environment variables
    connection_string = os.getenv("CONNECTION_STRING")
    if not connection_string:
        print("Error: CONNECTION_STRING environment variable is not set.")
        sys.exit(1)

    # Delete the data
    delete_category_data(args.domain_name, connection_string)


if __name__ == "__main__":
    main()
