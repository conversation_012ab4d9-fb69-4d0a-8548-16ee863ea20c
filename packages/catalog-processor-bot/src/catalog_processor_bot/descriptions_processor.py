import time
from pathlib import Path
from typing import Any

import diskcache
import loguru
import pandas as pd
from langchain.chat_models import init_chat_model
from langchain.prompts import PromptTemplate
from pgvector.sqlalchemy import Vector
from platformdirs import user_cache_dir
from sqlalchemy import create_engine, text, types, inspect
from sqlalchemy.dialects.postgresql import TEXT

from catalog_processor_bot.categories_table import _get_category_id
from catalog_processor_bot.embedding_service.embedding_service import embedding_dim, generate_embeddings

# Constants
DESCRIPTIONS_TABLE_NAME = "product_descriptions"


def _remove_stale_entries(conn, existing_ids, current_ids, category_id, logger=loguru.logger):
    """Remove entries from the database that exist in existing_ids but not in current_ids.

    Args:
        conn: Database connection
        existing_ids: List of IDs currently in the database
        current_ids: List of IDs in the current DataFrame
        category_id: The category ID to filter by
    """
    ids_to_remove = set(existing_ids) - set(current_ids)

    if ids_to_remove:
        conn.execute(
            text(f"DELETE FROM {DESCRIPTIONS_TABLE_NAME} WHERE id IN :ids AND category_id = :category_id"),
            {"ids": tuple(ids_to_remove), "category_id": category_id},
        )
        logger.info(f"Removed {len(ids_to_remove)} stale entries from {DESCRIPTIONS_TABLE_NAME}")


cache = diskcache.Cache(str(Path(user_cache_dir("catalog-feed-bot", "robosales")) / "llm_cache"))

@cache.memoize(ignore={"llm", "logger"}, expire=3600*24)
def llm_cached(*, row_id, llm, logger, prompt_template, description, other_fields_str):
    template = PromptTemplate.from_template(prompt_template)
    prompt = template.format(description=description, other_fields=other_fields_str)
    response = llm.invoke(prompt)
    logger.info(f"LLM altered product id: {row_id} with altered description: {response.content}")
    return response.content.strip()


def _process_descriptions(df: pd.DataFrame, llm: Any, logger) -> pd.DataFrame:
    """Process descriptions using LLM to remove redundant information."""
    prompt_template = (
        """
        Change the following product description, but exclude information that is already present in the data table.
        If the description mentions anything from the data table, remove it from the description.
        Do not add any additional information.
        Be accurate. Keep the answer short, but do not leave out any important information beside the data table.
        Description: {description}\n
        Data table to be excluded from description: {other_fields}\n
        Return the description in English language.
        """
    )

    def generate_altered_description(row):
        description = row["description"]
        if not description or pd.isna(description):
            return ""

        other_fields = {
            col: row[col]
            for col in row.index
            if col
            not in ["description", "rank", "id", "link", "image_link", "item_group_id", "link_master", "image_link_master", "group_title"]
            and pd.notna(row[col])
            and row[col] not in ["", "nan"]
        }
        other_fields_str = "; ".join([f"{k}: {v}" for k, v in other_fields.items()])
        return llm_cached(row_id=row["id"], llm=llm, logger=logger, prompt_template=prompt_template, description=description, other_fields_str=other_fields_str)


    df["description_cleaned"] = df.apply(generate_altered_description, axis=1)
    return df


def _create_descriptions_table_if_not_exists(connection: any) -> None:
    """Create the product_descriptions table if it doesn't exist."""
    # Enable pgvector extension if not already done
    connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))

    # Check if the table exists
    table_exists = inspect(connection).has_table(DESCRIPTIONS_TABLE_NAME)

    # If table doesn't exist, create it with the required columns
    if not table_exists:
        print(f"Creating table {DESCRIPTIONS_TABLE_NAME}...")
        connection.execute(
            text(
                f"""
            CREATE TABLE {DESCRIPTIONS_TABLE_NAME} (
                id TEXT PRIMARY KEY,
                item_group_id TEXT NOT NULL,
                domain_name TEXT NOT NULL,
                category_id INTEGER NOT NULL,
                description TEXT,
                description_cleaned TEXT,
                description_cleaned_embed vector({embedding_dim}),
                CONSTRAINT fk_category
                    FOREIGN KEY(category_id) 
                    REFERENCES catalog_categories(id)
                    ON DELETE SET NULL
            )
        """
            )
        )
        connection.commit()
        print(f"Created new table {DESCRIPTIONS_TABLE_NAME}")


def _check_existing_descriptions_for_category(connection_string: str, category_id: int) -> tuple[bool, bool]:
    """
    Check existing descriptions for a given category_id.

    Returns:
        tuple[bool, bool]: (category_exists_in_db, has_non_null_descriptions)
        - category_exists_in_db: True if category_id exists in the table
        - has_non_null_descriptions: True if any descriptions are not None for this category
    """
    engine = create_engine(connection_string)
    try:
        with engine.connect() as conn:
            # Check if table exists first
            table_exists = inspect(conn).has_table(DESCRIPTIONS_TABLE_NAME)
            if not table_exists:
                return False, False

            # Check if category exists in the table
            result = conn.execute(
                text(f"SELECT COUNT(*) FROM {DESCRIPTIONS_TABLE_NAME} WHERE category_id = :category_id"), {"category_id": category_id}
            ).fetchone()

            category_exists_in_db = result[0] > 0

            if not category_exists_in_db:
                return False, False

            # Check if any descriptions are not None for this category
            result = conn.execute(
                text(f"SELECT COUNT(*) FROM {DESCRIPTIONS_TABLE_NAME} WHERE category_id = :category_id AND description IS NOT NULL"),
                {"category_id": category_id},
            ).fetchone()

            has_non_null_descriptions = result[0] > 0

            conn.commit()

            return category_exists_in_db, has_non_null_descriptions

    except Exception as e:
        print(f"Error checking existing descriptions for category {category_id}: {e}")
        raise
    finally:
        engine.dispose()


def _get_existing_ids_for_category(connection_string: str, category_id: int) -> set[str]:
    """Get all existing IDs for a given category_id from the descriptions table."""
    engine = create_engine(connection_string)
    try:
        with engine.connect() as conn:
            # Check if table exists first
            table_exists = inspect(conn).has_table(DESCRIPTIONS_TABLE_NAME)
            if not table_exists:
                return set()

            result = conn.execute(
                text(f"SELECT id FROM {DESCRIPTIONS_TABLE_NAME} WHERE category_id = :category_id"), {"category_id": category_id}
            ).fetchall()

            conn.commit()

            return {row[0] for row in result}

    except Exception as e:
        print(f"Error getting existing IDs for category {category_id}: {e}")
        raise
    finally:
        engine.dispose()


from sqlalchemy.dialects.postgresql import insert

def insert_on_conflict_nothing(table, conn, keys, data_iter):
    data = [dict(zip(keys, row)) for row in data_iter]
    stmt = insert(table.table).values(data).on_conflict_do_nothing(index_elements=["id"])
    result = conn.execute(stmt)
    return result.rowcount


def _save_df_to_descriptions_table(df: pd.DataFrame, connection_string: str) -> None:
    """Save DataFrame to the descriptions database table."""

    engine = create_engine(connection_string)

    try:
        df.to_sql(
            name=DESCRIPTIONS_TABLE_NAME,
            con=engine,
            if_exists="append",
            index=False,
            dtype={
                "description_cleaned_embed": Vector(embedding_dim) if "Vector" in globals() else None,
                "id": TEXT,
                "item_group_id": TEXT,
                "domain_name": TEXT,
                "category_id": types.Integer,
                "description": TEXT,
                "description_cleaned": TEXT,
            },
            method=insert_on_conflict_nothing
        )
    finally:
        engine.dispose()


def save_new_descriptions_to_db(
    df: pd.DataFrame,
    connection_string: str,
    domain_name: str = "",
    model: str = "gemini-2.0-flash",
    model_provider: str = "google_genai",
    temperature: float = 0,
) -> None:
    """Process and save descriptions for a new category. To be used localy in this project."""
    df_copy = df.copy()

    engine = create_engine(connection_string)
    try:
        with engine.connect() as conn:
            _create_descriptions_table_if_not_exists(conn)
            conn.commit()
    finally:
        engine.dispose()

    # Initialize description columns with null values
    df_copy["description"] = df_copy.get("description", pd.NA)
    df_copy["description_cleaned"] = pd.NA
    df_copy["description_cleaned_embed"] = [None] * len(df_copy)

    # Only process if we have non-null descriptions
    if not df_copy["description"].isnull().all():
        # Process descriptions with LLM
        llm = init_chat_model(
            model=model,
            model_provider=model_provider,
            temperature=temperature,
        )
        print("Processing descriptions using LLM.")
        df_copy = _process_descriptions(df_copy, llm)

        # Generate embeddings for cleaned descriptions if available
        if not df_copy["description_cleaned"].isnull().all():
            descriptions_list = df_copy["description_cleaned"].fillna("").tolist()
            df_copy["description_cleaned_embed"] = generate_embeddings(descriptions_list)

    # Create a new DataFrame to be saved into product_descriptions table
    df_desc = df_copy[["id", "item_group_id", "description", "description_cleaned", "description_cleaned_embed"]].copy()
    df_desc["domain_name"] = domain_name
    df_desc["category_id"] = _get_category_id(connection_string, domain_name)

    # Save to database
    _save_df_to_descriptions_table(df_desc, connection_string)


def update_descriptions_table(
    df: pd.DataFrame,
    connection_string: str,
    logger,
    domain_name: str = "",
    model: str = "gemini-2.0-flash",
    model_provider: str = "google_genai",
    temperature: float = 0,
) -> None:
    """
    Update existing descriptions in the descriptions table. Use in Lambda function.
    """
    df_copy = df.copy()

    # Get category_id for the domain_name
    category_id = _get_category_id(connection_string, domain_name)

    # Get existing IDs for this category (assumes table exists and has records)
    existing_ids = _get_existing_ids_for_category(connection_string, category_id)
    logger.info(f"There are {len(existing_ids)} existing IDs in the database and {len(df_copy['id'].unique())} current IDs in the DataFrame.")

    # Remove stale entries
    engine = create_engine(connection_string)
    with engine.connect() as conn:
        _remove_stale_entries(conn, existing_ids, df_copy["id"].unique(), category_id, logger=logger)
        conn.commit()

    # Filter to only process rows with IDs not yet in database (avoiding duplicates)
    rows_to_process = df_copy[~df_copy["id"].isin(existing_ids)].copy()

    if rows_to_process.empty:
        logger.info(f"All IDs for category_id {category_id} already exist in {DESCRIPTIONS_TABLE_NAME} table. No processing needed.")
        return

    logger.info(f"Processing {len(rows_to_process)} new rows out of {len(df_copy)} total rows for category_id {category_id}")

    # Initialize description columns with null values
    rows_to_process["description"] = rows_to_process.get("description", pd.NA)
    rows_to_process["description_cleaned"] = pd.NA
    rows_to_process["description_cleaned_embed"] = [None] * len(rows_to_process)

    # Only process if we have non-null descriptions
    if not rows_to_process["description"].isnull().all():
        # Process descriptions with LLM
        llm = init_chat_model(
            model=model,
            model_provider=model_provider,
            temperature=temperature,
        )
        logger.info("Processing descriptions using LLM.")
        rows_to_process = _process_descriptions(rows_to_process, llm, logger=logger)

        # Generate embeddings for cleaned descriptions if available
        if not rows_to_process["description_cleaned"].isnull().all():
            descriptions_list = rows_to_process["description_cleaned"].fillna("").tolist()
            rows_to_process["description_cleaned_embed"] = generate_embeddings(descriptions_list)

    # ---- Database Operations ----
    # Create descriptions DataFrame for SQL insertion from processed rows
    df_desc = rows_to_process[["id", "item_group_id", "description", "description_cleaned", "description_cleaned_embed"]].copy()
    df_desc["domain_name"] = domain_name
    df_desc["category_id"] = category_id

    _save_df_to_descriptions_table(df_desc, connection_string)
