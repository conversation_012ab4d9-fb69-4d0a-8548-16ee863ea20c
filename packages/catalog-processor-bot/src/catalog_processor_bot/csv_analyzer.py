#!/usr/bin/env python3
"""
CSV Analyzer - Analyzes CSV files and provides column statistics.

This script analyzes CSV files, excluding specified columns, and provides:
- For numerical columns: range of values
- For text columns: up to 15 unique values
"""

import argparse
import textwrap
from datetime import datetime
from pathlib import Path
from typing import Dict, Union

import pandas as pd
import yaml

# Columns to exclude from analysis
EXCLUDED_COLUMNS = [
    "title",
    "description",
    "id",
    "link",
    "image_link",
    "availabilty",
    "price_czk",
    "brand",
    "rank",
    "item_group_id",
    "image_link_master",
    "link_master",
    "group_title",
]


def is_numeric(series: pd.Series) -> bool:
    """Check if a pandas Series contains numeric data."""
    return pd.api.types.is_numeric_dtype(series)


def analyze_csv(file_path: Path) -> Dict[str, Dict[str, Union[str, bool]]]:
    """
    Analyze a CSV file and return statistics for each column.

    Args:
        file_path: Path to the CSV file

    Returns:
        Dictionary with column names as keys and their analysis as values.
        Each value is a dictionary with 'type' (either 'numeric' or 'text') and 'details' (analysis string).
    """
    try:
        # Read CSV file
        df = pd.read_csv(file_path, low_memory=False)

        # Exclude specified columns
        columns_to_analyze = [col for col in df.columns if col.lower() not in [x.lower() for x in EXCLUDED_COLUMNS]]
        df = df[columns_to_analyze]

        results = {}

        for column in df.columns:
            series = df[column].dropna()  # Remove NaN values

            if is_numeric(series):
                # For numerical columns, get range
                min_val = series.min()
                max_val = series.max()
                results[column] = {"type": "numeric", "details": f"Range: {min_val} - {max_val}"}
            else:
                # For text columns, get unique values (up to 15)
                unique_vals = series.astype(str).unique()
                unique_count = len(unique_vals)

                if unique_count <= 15:
                    details = f"Unique values ({unique_count}): {', '.join(map(str, unique_vals))}"
                else:
                    details = f"{unique_count} unique values. First 15: {', '.join(map(str, unique_vals[:15]))}"
                results[column] = {"type": "text", "details": details}

        return results

    except Exception as e:
        return {"error": f"Error analyzing CSV: {str(e)}"}


def print_analysis_results(results: Dict, file_name: str) -> None:
    """
    Print the analysis results to the console.

    Args:
        results: Dictionary containing the analysis results
        file_name: Name of the file being analyzed
    """
    print(f"\nAnalyzing: {file_name}")
    print("-" * 50)

    if "error" in results:
        print(f"Error: {results['error']}")
        return

    if not results:
        print("No columns to analyze after excluding specified columns.")
        return

    for column, analysis in results.items():
        # Get the data type from the analysis if it's a dictionary, otherwise use the string
        if isinstance(analysis, dict):
            data_type = analysis.get("type", "unknown")
        else:
            data_type = "see details below"
        print(f"\n{column}(type: {data_type}):")
        print(f"  {analysis.get('details', 'No details available')}")

    # Ensure the data directory exists
    output_dir = Path("data")
    output_dir.mkdir(exist_ok=True)

    # Generate timestamp for filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_filename = f"analysis_{file_name}"

    # Save to markdown
    md_path = output_dir / f"{base_filename}.md"
    save_analysis_to_markdown(results, md_path)

    # Save to YAML
    yaml_path = output_dir / f"{base_filename}.yml"
    save_analysis_to_yaml(results, yaml_path)

    print(f"\nAnalysis saved to:")
    print(f"- Markdown: {md_path}")
    print(f"- YAML: {yaml_path}")
    print("Analysis complete!")


def save_analysis_to_markdown(analysis_results: Dict, output_path: Path) -> None:
    """
    Save the analysis results to a markdown file.

    Args:
        analysis_results: Dictionary containing the analysis results
        output_path: Path where to save the markdown file
    """
    # Ensure the output directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Prepare the markdown content
    markdown_lines = ["# CSV Analysis Report", f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", ""]

    for column, analysis in analysis_results.items():
        if isinstance(analysis, dict):
            data_type = analysis.get("type", "unknown")
            details = analysis.get("details", "No details available")
        else:
            data_type = "see details below"
            details = str(analysis)

        markdown_lines.extend([f"## `{column}`", f"- **Type:** `{data_type}`", f"- **Analysis:**"])

        # Format the details with proper line wrapping and indentation
        wrapped_details = textwrap.fill(details, width=80, subsequent_indent="  ")
        markdown_lines.append(f"  {wrapped_details}")
        markdown_lines.append("")

    # Write to the file (this will overwrite if file exists)
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("\n".join(markdown_lines))


def save_analysis_to_yaml(analysis_results: Dict, output_path: Path) -> None:
    """
    Save the analysis results to a YAML file.

    Args:
        analysis_results: Dictionary containing the analysis results
        output_path: Path where to save the YAML file
    """
    # Ensure the output directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Prepare the data for YAML serialization
    yaml_data = {"metadata": {"generated_at": datetime.now().isoformat(), "tool": "CSV Analyzer", "version": "1.0.0"}, "analysis": {}}

    # Convert the analysis results to a YAML-serializable format
    for column, analysis in analysis_results.items():
        if isinstance(analysis, dict):
            yaml_data["analysis"][column] = {
                "type": analysis.get("type", "unknown"),
                "details": analysis.get("details", "No details available"),
            }
        else:
            yaml_data["analysis"][column] = str(analysis)

    # Write to the YAML file
    with open(output_path, "w", encoding="utf-8") as f:
        yaml.dump(yaml_data, f, allow_unicode=True, sort_keys=False, default_flow_style=False)


def main():

    import dotenv
    dotenv.load_dotenv()

    # Set up argument parser
    parser = argparse.ArgumentParser(description="Analyze CSV file columns")
    parser.add_argument("csv_file", type=str, help="Path to the CSV file to analyze")
    args = parser.parse_args()

    # Convert to Path object
    csv_path = Path(args.csv_file)

    # Check if file exists
    if not csv_path.exists():
        print(f"Error: File '{csv_path}' not found.")
        return

    # Analyze the CSV
    results = analyze_csv(csv_path)

    # Print and save results
    print_analysis_results(results, csv_path.name)


if __name__ == "__main__":
    main()
