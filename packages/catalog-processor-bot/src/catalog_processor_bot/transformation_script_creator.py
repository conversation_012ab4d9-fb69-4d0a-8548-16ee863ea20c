from pathlib import Path

from catalog_processor_bot.get_table_name import get_table_name


def create_transformation_file(domain_name: str):
    """Create a new transformation file for the specified domain."""

    # Create the directory if it doesn't exist
    output_dir = Path(__file__).parent / "custom_products_transformations"
    output_dir.mkdir(exist_ok=True)

    # Create the output file path
    # Replace spaces and special characters with underscores to create a valid Python module name
    safe_domain_name = get_table_name(domain_name)
    output_file = output_dir / f"{safe_domain_name}.py"

    # Get the content of the custom_transformation.py file
    custom_transformation_path = Path(__file__).parent.parent / "custom_transformation.py"

    try:
        with open(custom_transformation_path, "r") as f:
            content = f.read()

        # Extract the code between imports and main()
        import re

        # Pattern to match everything after the import and before def main()
        code_pattern = r"(?s)from catalog_processor_bot\.post_processing import clean_column_of_spaces(.*?)(?=def main\()"
        match = re.search(code_pattern, content)
        if match:
            function_code = match.group(1).strip()

        if not match:
            print("Error: Could not find the transformation code section in custom_transformation.py")
            return False

        if "perform_custom_transformation" in function_code:
            function_code = function_code.replace("perform_custom_transformation", "process")
        else:
            print("Error: Could not find the perform_custom_transformation function in custom_transformation.py")
            raise ValueError("Could not find the perform_custom_transformation function in custom_transformation.py")

        # Create the new file with necessary imports and the renamed function
        new_file_content = """import re
import numpy as np
import pandas as pd
from typing import List


"""
        new_file_content += function_code

        # Write the new file
        with open(output_file, "w") as f:
            f.write(new_file_content)

        print(f"Successfully created {output_file}")
        return True

    except Exception as e:
        print(f"Error creating transformation file: {e}")
        return False
