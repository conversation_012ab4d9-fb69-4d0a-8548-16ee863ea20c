import argparse
import os
import shutil
import sys
import json
import tempfile
from pathlib import Path
from typing import List, Set

import <PERSON><PERSON>mer
import bm25s
import boto3
import loguru
import numpy as np
import pandas as pd
import yaml
from sqlalchemy import create_engine, text
from sqlalchemy import types, inspect
from sqlalchemy.orm import sessionmaker

from catalog_processor_bot.categories_table import (
    get_all_categories,
    check_if_domain_exists,
    _get_category_id,
)
from catalog_processor_bot.descriptions_processor import save_new_descriptions_to_db
from catalog_processor_bot.embedding_service.embedding_service import generate_embeddings
from catalog_processor_bot.get_table_name import get_table_name
from catalog_processor_bot.post_processing import convert_numeric_columns_to_int_floats
from catalog_processor_bot.transformation_script_creator import create_transformation_file
from catalog_processor_bot.categories_table import CatalogCategory

EMBEDDINGS_TABLE_NAME = "embeddings_for_npf"


def load_csv_to_dataframe(csv_file: str) -> pd.DataFrame:
    """Load CSV file from the data directory."""
    data_dir = Path(__file__).parent.parent.parent
    csv_path = data_dir / csv_file
    return pd.read_csv(csv_path)


def save_main_table(
    df: pd.DataFrame,
    table_name: str,
    connection_string: str,
    logger,
) -> None:
    """Save DataFrame to the main database table with foreign key constraint."""
    engine = create_engine(connection_string)
    table_name = f"products_{get_table_name(table_name)}"

    try:
        with engine.connect() as conn:
            # First save the data without the foreign key constraint
            df.to_sql(
                name=table_name,
                con=conn,
                if_exists="replace",
                index=False,
            )

            # Add the foreign key constraint
            alter_table_sql = f"""
            ALTER TABLE {table_name}
            ADD CONSTRAINT fk_product_descriptions
            FOREIGN KEY (id)
            REFERENCES product_descriptions(id)
            ON DELETE CASCADE
            """
            conn.execute(text(alter_table_sql))
            conn.commit()

        logger.info(f"Successfully saved to main table '{table_name}' with foreign key constraint")
    finally:
        engine.dispose()


def _get_unique_strings_from_db(df: pd.DataFrame, columns: List[str]) -> List[str]:
    """Extracts unique non-null string values from specified columns in a DataFrame."""
    unique_strings: Set[str] = set()

    for col in columns:
        if col in df.columns:
            try:
                # Get unique non-null values and filter for strings
                unique_values = df[col].dropna().astype(str).unique()
                unique_strings.update(val for val in unique_values if isinstance(val, str) and val.strip())
            except Exception as col_err:
                print(f"Error processing column '{col}': {col_err}")

    return sorted(list(unique_strings))


def _extract_bytes(embedding: List[float]) -> bytes:
    """Converts a list of floats (embedding) into bytes."""
    return np.array(embedding, dtype=np.float64).tobytes()


def _create_embeddings_table(connection: any) -> None:
    """Create the embeddings_for_npf table if it doesn't exist."""
    # Create an extension if not exists
    connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))

    # Create an embeddings_for_npf table with a foreign key if it doesn't exist
    table_exists = inspect(connection).has_table(EMBEDDINGS_TABLE_NAME)
    if not table_exists:
        connection.execute(
            text(
                f"""
            CREATE TABLE {EMBEDDINGS_TABLE_NAME} (
                word TEXT NOT NULL,
                "binary" BYTEA NOT NULL,
                domain_name TEXT,
                category_id INTEGER,
                CONSTRAINT fk_category
                    FOREIGN KEY(category_id)
                    REFERENCES catalog_categories(id)
                    ON DELETE SET NULL
            )
        """
            )
        )
        connection.commit()
        print(f"Created new table {EMBEDDINGS_TABLE_NAME} with foreign key constraint")


def _check_and_prepare_embeddings(
    all_unique_values: List[str], connection_string: str, connection: any, domain_name: str = "", logger=loguru.logger,
) -> List[str]:
    """Check for existing embeddings and prepare the list of values to embed."""
    engine = create_engine(connection_string)

    # First, check if the table exists
    table_exists = inspect(connection).has_table(EMBEDDINGS_TABLE_NAME)
    if not table_exists:
        logger.warning(f"{EMBEDDINGS_TABLE_NAME} table does not exist. Will embed all unique values.")
        return all_unique_values

    # Check for existing values with the same domain_name
    if domain_name:
        logger.info(f"Checking for existing values with domain_name '{domain_name}'...")
        query = f"""
                SELECT word
                FROM {EMBEDDINGS_TABLE_NAME}
                WHERE domain_name = :domain_name
                """
        existing_words = pd.read_sql(text(query), engine, params={"domain_name": domain_name})["word"].tolist()

        # Filter out existing words
        values_to_embed = [val for val in all_unique_values if val not in existing_words]

        if not values_to_embed:
            logger.info("All unique values already exist in embeddings table for this domain.")
            return []

        logger.info(f"Found {len(existing_words)} existing values, will embed {len(values_to_embed)} new values.")
        return values_to_embed
    else:
        logger.info("No domain_name provided, will embed all unique values without checking for existing ones.")
        return all_unique_values


def generate_and_save_embeddings(df: pd.DataFrame, connection_string: str, domain_name: str = "", logger=loguru.logger) -> None:
    """Generate and save embeddings for unique string values from the DataFrame."""

    engine = create_engine(connection_string)

    # Define columns to process
    cols_to_exclude_from_embedding = [
        "description",
        "rank",
        "id",
        "link",
        "image_link",
        "item_group_id",
        "link_master",
        "image_link_master",
        "description_cleaned",
    ]

    # Get string columns to process
    cols_to_extract = [col for col in df.columns if df[col].dtype == "object" and col not in cols_to_exclude_from_embedding]

    if not cols_to_extract:
        logger.warning("No columns to process for embeddings.")
        return

    logger.info(f"Processing columns for embeddings: {cols_to_extract}")

    # Get unique strings
    all_unique_values = _get_unique_strings_from_db(df, cols_to_extract)

    if not all_unique_values:
        logger.info("No unique string values found to embed.")
        return

    logger.info(f"Found {len(all_unique_values)} unique string values to embed.")

    # Check and prepare values to embed
    with engine.connect() as conn:
        values_to_embed = _check_and_prepare_embeddings(all_unique_values, connection_string, conn, domain_name, logger=logger)
        if not values_to_embed:
            return

    # Generate Embeddings for only the new values
    logger.info("Generating embeddings...")
    embeddings_list = generate_embeddings(values_to_embed)
    logger.info(f"Generated {len(embeddings_list)} embeddings with dimension {len(embeddings_list[0])}.")

    # Convert embeddings to bytes and create DataFrame
    embeds_b = list(map(_extract_bytes, embeddings_list))
    df_embeddings = pd.DataFrame({"word": values_to_embed, "binary": embeds_b})

    # Add domain_name and category_id if domain_name is provided
    if domain_name:
        df_embeddings["domain_name"] = domain_name
        category_id = _get_category_id(connection_string, domain_name)
        if category_id is not None:  # This should always be true now since we raise on not found
            df_embeddings["category_id"] = category_id
            df_embeddings["category_id"] = df_embeddings["category_id"].astype("Int64")  # Allow NULLs

    # Save to a database
    logger.info(f"Saving {len(df_embeddings)} embeddings to table {EMBEDDINGS_TABLE_NAME}...")

    # Create the table if it doesn't exist
    with engine.connect() as conn:
        _create_embeddings_table(conn)

    # Save the embeddings
    df_embeddings.to_sql(
        name=EMBEDDINGS_TABLE_NAME, con=engine, if_exists="append", index=False, dtype={"binary": types.LargeBinary}
    )
    logger.info(f"Successfully saved {len(df_embeddings)} embeddings to table {EMBEDDINGS_TABLE_NAME}")

    engine.dispose()


def _get_s3_client():
    """Get an S3 client, automatically determining the appropriate credentials."""
    return boto3.client("s3")


def create_bm25_index(connection_string: str, index_name: str, logger=loguru.logger):
    """Create and save a BM25 index for product descriptions to S3."""

    # Get S3 client
    s3_client = _get_s3_client()
    s3_bucket_name = "bm25-indices"
    s3_prefix = index_name.replace("> ", "")

    # Ensure the S3 prefix ends with a slash
    if not s3_prefix.endswith("/"):
        s3_prefix += "/"

    # Load the descriptions from the database
    engine = create_engine(connection_string)
    query = f"""
        SELECT * 
        FROM product_descriptions 
        WHERE domain_name = '{index_name}'
    """
    df_descriptions = pd.read_sql(query, engine)

    # Prepare the corpus
    corpus = [str(desc) for desc in df_descriptions["description_cleaned"] if pd.notna(desc)]

    if not corpus:
        logger.warning("No valid descriptions found for BM25 indexing.")
        return

    # Initialize stemmer and tokenize the corpus
    stemmer = Stemmer.Stemmer("english")
    corpus_tokens = bm25s.tokenize(corpus, stopwords="en", stemmer=stemmer)

    # Create the BM25 index
    retriever = bm25s.BM25()
    retriever.index(corpus_tokens)

    # Save to a temporary directory first
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir) / "bm25_index"
        # Save to temp directory
        retriever.save(temp_path, corpus=corpus)

        # Upload all files from the temp directory to S3
        for file_path in temp_path.glob("*"):
            if file_path.is_file():
                s3_key = f"{s3_prefix}{file_path.name}"
                s3_client.upload_file(Filename=str(file_path), Bucket=s3_bucket_name, Key=s3_key)

        logger.info(f"Successfully created and uploaded BM25 index to s3://{s3_bucket_name}/{s3_prefix}")


def create_json_category(df: pd.DataFrame, connection_string: str, domain_name: str = "", logger=loguru.logger):
    """Convert DataFrame to JSON and update the catalog_categories table."""

    try:
        # Convert DataFrame to list of dictionaries and serialize to JSON
        records = df.to_dict('records')
        json_data = json.dumps(records, ensure_ascii=False, default=str)

        logger.info(f"Converted {len(records)} records to JSON for domain '{domain_name}'")

        # Connect to database and update catalog_json
        engine = create_engine(connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            # Find the category by domain name
            category = session.query(CatalogCategory).filter(CatalogCategory.name == domain_name).first()

            if not category:
                logger.error(f"Domain '{domain_name}' not found in catalog_categories table")
                return

            # Update the catalog_json column
            category.catalog_json = json_data
            session.commit()

            logger.info(f"Successfully updated catalog_json for domain '{domain_name}'")

        except Exception as e:
            session.rollback()
            raise
        finally:
            session.close()
            engine.dispose()

    except Exception as e:
        logger.error(f"Error updating catalog_json for domain '{domain_name}': {e}")
        raise


def update_config_yaml(config_file, domain_name, df, table_name):

    with open(config_file, "r", encoding='utf-8') as f:
        cfg = yaml.safe_load(f)

    new_config = _generate_config(df, domain_name, table_name)

    if cfg is None:
        cfg = {}

    cfg["domain_settings"] = cfg.get("domain_settings", {})
    cfg["domain_settings"][domain_name] = new_config["domain_settings"][domain_name]

    shutil.copy(config_file, config_file + ".bkp")

    with open(config_file, "w", encoding='utf-8') as f:
        yaml.dump(cfg, f, allow_unicode=True)

def save_config_yaml(domain_name: str, df: pd.DataFrame, table_name: str, config_dir=None) -> None:
    """Generate and save a configuration YAML file based on the template in config_orig.yml."""
    try:
        if config_dir is None:
            config_dir = Path(__file__).parent.parent.parent / "config"
        config_dir.mkdir(parents=True, exist_ok=True)

        config = _generate_config( df, domain_name, table_name)

        # Save the config to a YAML file
        config_path = config_dir / f"{domain_name.replace(' ', '_').replace('>', '').replace('<', '')}_config.yml"
        with open(config_path, "w", encoding="utf-8") as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, sort_keys=False)

        print(f"Successfully created config file at {config_path}")

    except Exception as e:
        print(f"Error creating config YAML file: {e}")
        raise


def _generate_config(df, domain_name, table_name):
    cols_to_leave_out = [
        "description",
        "id",
        "link",
        "image_link",
        "item_group_id",
        "link_master",
        "image_link_master",
        "description_cleaned_embed",
        "description_cleaned",
        "group_title",
        "rank",
    ]
    cols_to_extract = [col for col in df.columns if (col not in cols_to_leave_out)]
    all_columns = cols_to_extract
    fetch_columns = [
        col
        for col in all_columns
        if col.lower()
           not in [
               "title",
               "material",
               "materiál",
               "modelová řada",
               "používá",
               "barva",
           ]
    ]
    if "description" in df.columns:
        fetch_columns.append("description")
    color_column = next((col for col in all_columns if col.lower() in ["barva", "color"]), None)
    categorical_columns = [
        col for col in all_columns if
        col.lower() not in ["barva", "title"] and df[col].nunique() < 6 and df[col].dtype == "object"
    ]
    # Generate table_instructions
    table_instructions = (
                             "if the question contains series, use column Modelová řada." if "Modelová řada" in df.columns else "") + (
                             "Use czech names of colors for column `barva`."
                             "If the question contains combination of colors, use `&` operator for individual colors in colors subcondition."
                             if any(col.lower() in ["barva", "color"] for col in df.columns)
                             else ""
                         ) + "If the question contains words like big, high, large, small, low, cheap, expensive, and such for other paremeters than weight, search for lowest or highes 30 % values of that parameters." "If the question contains words like biggest, highest, largest, smallest, lowest, cheapest, most expensive and such for other parameters than weight, search for minimal or maximal values of that parameter." + (
                             "If the question is about which famous professional player uses what product, use column `Používá`."
                             if "Používá" in df.columns
                             else ""
                         ) + f"Do not recommend child {table_name}, unless you know the user is talking about a child."
    # Generate agent_instructions
    agent_instructions_without_description = f"You have access to product catalog of Sportega company that contains only this information for {table_name}: {[col for col in all_columns if col.lower() not in ['rank', 'title']]}"
    agent_instructions_with_description = (
        f"You have access to product catalog of Sportega company that contains all the possible information about {table_name}."
        "In the Final answer, you must tell whether the user´s question violates the recommended products descriptions."
    )
    agent_instructions = agent_instructions_with_description if "description" in df.columns else agent_instructions_without_description
    # Create config structure based on template
    config = {
        "domain_settings": {
            f"{domain_name}": {
                "category_type": "table",
                "products_table": f"products_{table_name}",
                "product_name": f"{table_name}",
                "optional_title_prefix": "",
                "search_columns": ",".join(all_columns),
                "fetch_columns": ",".join(fetch_columns),
                "color_column": color_column if color_column else None,
                "variant_column": None,
                "categorical_columns": ",".join(categorical_columns),
                "query_responser_llm": "gpt-4.1-2025-04-14",
                "table_instructions": table_instructions,
                "agent_instructions": agent_instructions,
                "npf_config": {
                    "variant_column_str": None,
                    "split_column_config": None,
                    "weights_override": None,
                    "categorical_cols": [col for col in categorical_columns],
                    "ordinal_mappings": None,
                    "metacategories": None,
                },
            }
        }
    }
    return config


def main(category_type: str = 'table'):


    import dotenv
    dotenv.load_dotenv()

    # Validate category_type parameter
    if category_type not in ['table', 'json']:
        print(f"Error: category_type must be 'table' or 'json', got '{category_type}'")
        sys.exit(1)

    parser = argparse.ArgumentParser(description="Load CSV data into a database table")
    parser.add_argument("--domain-name", dest="domain_name", default="", help="Domain name to associate with the embeddings")
    parser.add_argument("--category-type", dest="category_type", default=category_type, choices=['table', 'json'], help="Category type: 'table' or 'json' (default: table)")
    parser.add_argument("csv_file", help="Name of the CSV file in the data directory")

    args = parser.parse_args()

    # Load environment variables
    connection_string = os.getenv("CONNECTION_STRING")
    if not connection_string:
        print("Error: CONNECTION_STRING environment variable is not set.")
        sys.exit(1)

    if args.domain_name and check_if_domain_exists(domain_name=args.domain_name, connection_string=connection_string):
        table_name = get_table_name(args.domain_name)

        # Load the CSV file
        df = load_csv_to_dataframe(args.csv_file)
        if df.dropna(how="all").empty:
            print("Error: The loaded data contains no non-null rows.")
            sys.exit(1)

        # if id or item_group_id columns exist, convert them to string
        if "id" in df.columns:
            df["id"] = df["id"].astype(str)
        if "item_group_id" in df.columns:
            df["item_group_id"] = df["item_group_id"].astype(str)

        df = convert_numeric_columns_to_int_floats(df)

        # Execute different logic based on category_type
        if args.category_type == 'table':
            # Save descriptions
            save_new_descriptions_to_db(df=df, connection_string=connection_string, domain_name=args.domain_name)

            # Save the main table
            from loguru import logger
            save_main_table(df, args.domain_name, connection_string, logger)

            # Generate and save embeddings for unique string values
            generate_and_save_embeddings(df, connection_string, args.domain_name)

            # Check if 'description' column exists and has any non-null values
            if "description" in df.columns and not df["description"].isnull().all():
                # Save the BM25 index
                create_bm25_index(connection_string, args.domain_name, logger=logger)

            # Generate and save configuration YAML file
            save_config_yaml(args, df, table_name)

            # Create the custom products transformations script
            create_transformation_file(args.domain_name)

        elif args.category_type == 'json':
            # Call create_json_category function for JSON processing
            from loguru import logger
            create_json_category(df, connection_string, args.domain_name, logger=logger)

    else:
        # List all available domains
        domains = get_all_categories(connection_string)
        if domains:
            print("Available domains:")
            for domain in domains:
                print(f"  - {domain}")
            print("\nUse --domain-name DOMAIN_NAME to process a specific domain")
        else:
            print("No domains found in the database")
            sys.exit(1)


if __name__ == "__main__":
    main()
