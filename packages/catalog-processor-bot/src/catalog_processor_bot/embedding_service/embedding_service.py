import os
from typing import List

from langchain_openai import OpenAIEmbeddings

embedding_dim = 3072
embedding_model = "text-embedding-3-large"


def _get_embeddings_model() -> OpenAIEmbeddings:
    """Initialize and return the OpenAI embeddings model."""
    if "OPENAI_API_KEY" not in os.environ:
        raise ValueError("OPENAI_API_KEY environment variable not set.")
    return OpenAIEmbeddings(
        model=embedding_model,
        dimensions=embedding_dim,
        api_key=os.getenv("OPENAI_API_KEY"),
    )


def generate_embeddings(texts: List[str]) -> List[List[float]]:
    """Generate embeddings for a list of text strings."""
    embeddings_model = _get_embeddings_model()
    return embeddings_model.embed_documents([str(t) if t is not None else "" for t in texts])
