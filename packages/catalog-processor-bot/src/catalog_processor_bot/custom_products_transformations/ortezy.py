import os
import re
from typing import Optional

import pandas as pd

import re
import unidecode

def process_ortezy(df: pd.DataFrame) -> pd.DataFrame:
    """
    Process orthotics data with custom transformations.

    Args:
        df: Input DataFrame with orthotics data

    Returns:
        Processed DataFrame with additional columns and type conversions
    """
    # Create a copy to avoid SettingWithCopyWarning
    df = df.copy()

    def safe_float_conversion(series):
        """Safely convert series to float, handling NA/NaN values."""
        if pd.api.types.is_numeric_dtype(series):
            return series.astype('float64')

        return (
            series
            .astype(str)
            .str.extract(r'(\d+(?:\.\d+)?)', expand=False)  # Extract first number (with optional decimal)
            .replace(['', 'nan', 'None', 'NA', 'N/A', 'NaN'], pd.NA)  # Convert common NA representations
            .astype('float64')
        )

    # 1. Convert 'Hodnocení Sportega' to numerical
    if 'Hodnocení Sportega' in df.columns:
        df['Hodnocení Sportega'] = safe_float_conversion(df['Hodnocení Sportega'])

    # 2. Convert 'Počet ks v balení' to numerical
    if 'Počet ks v balení' in df.columns:
        df['Počet ks v balení'] = safe_float_conversion(df['Počet ks v balení'])

    # 3. Create 'stupen podpory_float' column
    if 'Stupeň podpory' in df.columns:
        df['Stupeň podpory'] = (
            df['Stupeň podpory']
            .astype(str)
            .str.extract(r'Level\s*(\d+)', expand=False)  # Extract the number after 'Level'
            .replace(['', 'nan', 'None', 'NA', 'N/A', 'NaN'], pd.NA)  # Convert common NA representations
            .astype('float64')
        )

    return df

def to_snake(col):
    col = unidecode.unidecode(col)      # remove accents
    col = re.sub(r'[^\w\s]', '', col)   # kill punctuation
    col = col.strip().lower()
    col = re.sub(r'[\s\-]+', '_', col)  # spaces/dashes to underscore
    return col

def to_snake_df(df):
    df = df.copy()
    df.columns = [to_snake(col) for col in df.columns]
    return df

def extract_typ_produktu(df):
    extracted = pd.Series(data=[None] * len(df), index=df.index)
    extracted[df.title.str.lower().str.contains('ortézy')] = 'ortéza'
    extracted[df.title.str.lower().str.contains('bandáž')] = 'bandáž'
    extracted[extracted.isna()] = 'ostatní'
    return extracted

def process(df: pd.DataFrame) -> pd.DataFrame:
    df = df.copy()
    df = process_ortezy(df)

    if "Počet ks v balení" in df.columns:
        df["Počet ks v balení"] = df["Počet ks v balení"].replace("1 pár", "2")
        df["Počet ks v balení"] = pd.to_numeric(df["Počet ks v balení"], errors="coerce")

    df = to_snake_df(df)
    df.loc[df.typ_produktu.isna(), 'typ_produktu'] = extract_typ_produktu(df[df.typ_produktu.isna()])
    df = df.drop(columns=["custom_label_0", "description", "size", "google_product_category", "product_type"], errors='ignore')

    return df
