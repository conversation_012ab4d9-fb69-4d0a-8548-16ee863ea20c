import re

import pandas as pd


def clean_measurements(text):
    """Remove measurements in format X.XX mm from the text"""
    if pd.isna(text):
        return text
    # Remove patterns like '1.28 mm' or '1.28mm' with optional spaces
    return re.sub(r"\s*\d+[\.,]?\d*\s*mm\b", "", str(text))


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations."""
    # Drop specified prefixes from title
    TITLE_PREFIXES_TO_DROP = ["Tenisový výplet", "Tenisové výplety"]
    for title_prefix in TITLE_PREFIXES_TO_DROP:
        df["title"] = df["title"].str.replace(f"^{re.escape(title_prefix)} ", "", regex=True)
        df["group_title"] = df["group_title"].str.replace(f"^{re.escape(title_prefix)} ", "", regex=True)

    # Drop specified columns if they exist
    CUSTOM_COLUMNS_TO_DROP = ["{http://base.google.com/ns/1.0}size"]
    df = df.drop(columns=[col for col in CUSTOM_COLUMNS_TO_DROP if col in df.columns])

    df["group_title"] = df["group_title"].apply(clean_measurements)

    # Fill all none values in "Velikost" column with values from "Průměr struny" column
    df["Velikost"] = df["Velikost"].fillna(df["Průměr struny"])
    # drop průměr struny column
    df = df.drop(columns=["Průměr struny"])
    # Rename columns
    df = df.rename(columns={"Velikost": "Průměr struny [mm]", "Typ výpletu": "Typ vlákna"})
    # delete all mm values and spaces from column 'Průměr struny [mm]'
    df["Průměr struny [mm]"] = df["Průměr struny [mm]"].str.replace(" mm", "")
    df["Průměr struny [mm]"] = df["Průměr struny [mm]"].str.replace(",", ".")
    df["Průměr struny X [mm]"] = df["Průměr struny [mm]"].str[:4]
    df["Průměr struny Y [mm]"] = df["Průměr struny [mm]"].str[-4:]

    # rename Maximální napětí struny column to 'Maximální napětí struny [kg]'
    df = df.rename(columns={"Maximální napětí struny": "Maximální napětí struny [kg]"})
    # strip the content of the kg and all spaces
    df["Maximální napětí struny [kg]"] = df["Maximální napětí struny [kg]"].str.replace("kg", "").str.replace(" ", "")
    df["Maximální napětí struny X [kg]"] = df["Maximální napětí struny [kg]"].str[:4]
    df["Maximální napětí struny Y [kg]"] = df["Maximální napětí struny [kg]"].str[-4:]

    # create new columns image_link_master and link_master
    df["image_link_master"] = df["image_link"]
    df["link_master"] = df["link"]

    # rename column Určeno pro kategorii hráčů to player_level
    df = df.rename(columns={"Určeno pro kategorii hráčů": "player_level", "Typ výletu": "Typ vlákna výpletu", "Balení": "Typ balení"})

    # delete all strings that says mírně a středně
    df["player_level"] = df["player_level"].str.replace("mírně", "").str.replace("středně", "")
    df["player_level"] = df["player_level"].str.replace(" a ", " ")

    # drop column hodnocení komfortu a počet vrstev
    df = df.drop(columns=["Hodnocení komfortu", "Počet vrstev"])

    # Extract text after last '>' in product_type append to 'Zaměření výpletu' with a comma separator
    df["product_type_suffix"] = df["product_type"].str.extract(r"([^>]*)$", expand=False).str.strip()
    df["Zaměření výpletu"] = df.apply(
        lambda x: (
            f"{x['Zaměření výpletu']}, {x['product_type_suffix']}"
            if pd.notna(x["product_type_suffix"]) and x["product_type_suffix"] != ""
            else x["Zaměření výpletu"]
        ),
        axis=1,
    )
    df = df.drop(columns=["product_type", "product_type_suffix"])

    df["group_title"] = df["group_title"].str.replace(r"\b\d\.\d+\b", "", regex=True)

    return df
