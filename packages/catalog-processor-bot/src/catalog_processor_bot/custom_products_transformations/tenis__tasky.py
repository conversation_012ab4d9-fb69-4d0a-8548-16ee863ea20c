import re

import pandas as pd


def extract_dimension_value(text):
    """Extract numeric value from dimension text, handling decimal commas."""
    if pd.isna(text):
        return None
    # Find first number in the text, allowing for decimal comma
    match = re.search(r"(\d+[,\d]*)", str(text).replace(" ", ""))
    if match:
        # Convert comma to dot for float conversion
        value_str = match.group(1).replace(",", ".")
        try:
            return float(value_str)
        except (ValueError, TypeError):
            return None
    return None


def extract_numeric_value(text):
    """Extract first integer value from text."""
    if pd.isna(text):
        return None
    # Convert to string if not already
    text = str(text)
    # Find the first number in the text
    match = re.search(r"\b(\d+)\b", text)
    if match:
        try:
            return int(match.group(1))
        except (ValueError, TypeError):
            return None
    return None


def standardize_boolean_column(df: pd.DataFrame, column_name: str) -> None:
    """Standardize a boolean column to contain only 'ano', 'ne', or None.

    Args:
        df: The DataFrame containing the column
        column_name: Name of the column to standardizei
    """
    if column_name in df.columns:
        # Replace any non-null values that are not 'ano' or 'ne' with 'ano'
        mask = (~df[column_name].isna()) & (~df[column_name].isin(["ano", "ne"]))
        df.loc[mask, column_name] = "ano"


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations."""
    # 1. Remove specified columns
    columns_to_drop = [
        "description",
        "Rozměry",
        "Modelová řada",
        "Objemová třída tašky",
        "Typ produktu",
        "Kapsa na notebook",
        "Dlouhý popruh na nošení",
        "Vhodnost pro sporty",
        "Počet velkých oddílů",
        "{http://base.google.com/ns/1.0}google_product_category",
    ]
    # Only drop columns that exist in the DataFrame
    columns_to_drop = [col for col in columns_to_drop if col in df.columns]
    df = df.drop(columns=columns_to_drop, errors="ignore")

    # 2. Extract dimensions from text columns to numeric [cm] columns
    dimension_mapping = {
        "Délka tašky / batohu": "Délka [cm]",
        "Šířka (hloubka) tašky / batohu": "Šířka [cm]",
        "Výška tašky / batohu": "Výška [cm]",
    }

    # List to store dimension text columns that exist in the DataFrame
    dimension_columns_to_drop = []

    for text_col, num_col in dimension_mapping.items():
        if text_col in df.columns and num_col in df.columns:
            # Only fill NaN values in the numeric column
            mask = df[num_col].isna() & df[text_col].notna()
            df.loc[mask, num_col] = df.loc[mask, text_col].apply(extract_dimension_value)
            dimension_columns_to_drop.append(text_col)

    # 3. Drop the original dimension text columns
    df = df.drop(columns=dimension_columns_to_drop, errors="ignore")

    # 4. Extract numeric values from various columns
    columns_to_clean = [
        "Maximální počet raket",
        "Počet oddílů na rakety",
        "Počet kapes přístupných z venkovní strany",
        "Počet kapes schovaných uvnitř tašky",
        "Počet velkých oddílů",
    ]

    for column in columns_to_clean:
        if column in df.columns:
            df[column] = df[column].apply(extract_numeric_value)

    # 5. Remove unwanted phrases from title
    if "title" in df.columns:
        # Check if 'na kolečkách' is in title and update product_type accordingly
        if df["title"].str.contains("na kolečkách", na=False).any():
            df.loc[df["title"].str.contains("na kolečkách", na=False), "product_type"] = "Taška na kolečkách"
    # 5. Remove unwanted phrases from title and group_title
    unwanted_phrases = ["na kolečkách", "na obuv"]
    for phrase in unwanted_phrases:
        df["title"] = df["title"].str.replace(phrase, "", regex=False)
        df["group_title"] = df["group_title"].str.replace(phrase, "", regex=False)

    # 5. Standardize boolean columns
    boolean_columns = [
        "Kapsy nebo oddíly s termo funkcí",
        "Samostatný oddíl na boty nebo vlhké oblečení",
        "Střední úchop (ucho na nošení v ruce)",
    ]

    for column in boolean_columns:
        standardize_boolean_column(df, column)

    # 6. Process 'Objem' column
    if "Objem" in df.columns:
        # Remove rows with 'neuvádí se'
        df = df[df["Objem"] != "neuvádí se"]

        # Extract numeric values and convert to float
        df["Objem"] = df["Objem"].apply(extract_numeric_value)

        # Rename the column to 'Objem [l]'
        df = df.rename(columns={"Objem": "Objem [l]"})

    # 9. Clean 'product_type' column to keep only text after last '>'
    if "product_type" in df.columns:
        df["product_type"] = df["product_type"].str.split(">").str[-1].str.strip()

    # 10. Clean 'title' and 'group_title' columns by removing common prefixes
    prefixes = ["Vak ", "Dětský batoh na rakety ", "Taška na rakety ", "Batoh na rakety ", "Dětská taška na rakety ", "Taška "]

    for col in ["title", "group_title"]:
        if col in df.columns:
            for prefix in prefixes:
                df[col] = df[col].str.replace(f"^{re.escape(prefix)}", "", regex=True)

    # 6. Strip all values in all columns
    df = df.map(lambda x: x.strip() if isinstance(x, str) else x)

    return df
