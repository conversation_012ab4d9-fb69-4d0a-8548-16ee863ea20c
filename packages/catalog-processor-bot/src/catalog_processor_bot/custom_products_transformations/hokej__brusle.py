import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Drops specified columns and performs data cleaning."""

    # Drop the specified columns if they exist
    columns_to_drop = [
        "description",
        "Preferovaný styl bruslení",
        "Tepelné tvarování",
        "<PERSON><PERSON>",
        "holder",
        "Holdery nožů",
        "Typ holderu",
        "{http://base.google.com/ns/1.0}size",
    ]

    # Remove columns that exist in the DataFrame
    existing_columns = [col for col in columns_to_drop if col in df.columns]
    df = df.drop(columns=existing_columns, errors="ignore")

    # Clean up any remaining whitespace in string columns
    string_columns = df.select_dtypes(include=["object"]).columns
    for col in string_columns:
        df[col] = df[col].astype(str).str.strip()
    return df
