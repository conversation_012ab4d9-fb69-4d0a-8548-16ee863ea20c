from typing import List

import pandas as pd


def remove_prefixes(text: str, prefixes: List[str]) -> str:
    """Remove specified prefixes from the beginning of the text."""
    if not isinstance(text, str):
        return text

    for prefix in prefixes:
        if text.startswith(prefix):
            text = text[len(prefix) :].strip()
    return text.strip()


def clean_column_of_spaces(df: pd.DataFrame) -> pd.DataFrame:
    columns_to_clean = []
    if "title" in df.columns:
        columns_to_clean.append("title")
    if "group_title" in df.columns:
        columns_to_clean.append("group_title")

    prefixes_to_remove = ["Potítko", "Potítka", "Čelenka", "Ručník", "Osuška"]

    for col in columns_to_clean:
        # Remove specified prefixes
        df[col] = df[col].apply(lambda x: remove_prefixes(x, prefixes_to_remove))
        # Clean up remaining text
        df[col] = df[col].str.replace(r"[\(\)]", "", regex=True)
        df[col] = df[col].str.replace(r"(\d),(\d)", r"\1.\2", regex=True)
        df[col] = df[col].str.replace(",", "", regex=True)
        df[col] = df[col].str.replace(r"\s+", " ", regex=True)
        df[col] = df[col].str.strip()

    return df


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations."""

    # 1. Clean titles first to remove prefixes and clean up text
    df = clean_column_of_spaces(df)

    # 2. Process "Počet ks v balení" column
    if "Počet ks v balení" in df.columns:
        df["Počet ks v balení"] = df["Počet ks v balení"].replace("1 pár", "2")
        df["Počet ks v balení"] = pd.to_numeric(df["Počet ks v balení"], errors="coerce")

    # 3. Process "Roční kolekce" column
    if "Roční kolekce" in df.columns:
        df["Roční kolekce"] = df["Roční kolekce"].astype(str).str.split("/").str[0]
        df["Roční kolekce"] = pd.to_numeric(df["Roční kolekce"], errors="coerce")

    # 4. Process "Typ produktu" column based on product_type
    if "product_type" in df.columns and "Typ produktu" in df.columns:
        # Get the last word after the last '>' in product_type
        last_words = df["product_type"].str.extract(r">([^>]*)$")[0].str.strip()

        # Create mapping based on conditions
        mapping = {"Potítka": "potítko", "Čelenky": "čelenka", "Ručníky": "ručník", "Osušky": "osuška"}

        # Apply the mapping to fill null values in "Typ produktu"
        for key, value in mapping.items():
            mask = (last_words == key) & (df["Typ produktu"].isna())
            df.loc[mask, "Typ produktu"] = value

    # 5. Drop specified columns if they exist
    columns_to_drop = [
        "description",
        "{http://base.google.com/ns/1.0}google_product_category",
        "{http://base.google.com/ns/1.0}size",
        "Rozměry",
        "product_type",
    ]
    columns_to_drop = [col for col in columns_to_drop if col in df.columns]
    df = df.drop(columns=columns_to_drop)

    return df
