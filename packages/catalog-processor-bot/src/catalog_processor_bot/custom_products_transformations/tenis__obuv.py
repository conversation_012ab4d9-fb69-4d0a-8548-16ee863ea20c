import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """
    Perform custom transformations on the DataFrame:
    1. Merge weight columns into a single column 'Hmotnost [g]'
    2. Drop the '{http://base.google.com/ns/1.0}size' column
    3. Handle gender column (fill NAs with 'kids' and rename to 'Pohlaví')
    4. Drop the '{http://base.google.com/ns/1.0}age_group' column
    5. Create 'Velikost [EUR]' column from 'Velikost' column
    """
    # Create a new column that combines values from both weight columns
    df["Hmotnost [g]"] = df["Hmotnost boty - muži"].fillna(df["Hmotnost boty - ženy"])

    # Handle gender column
    gender_col = "{http://base.google.com/ns/1.0}gender"
    if gender_col in df.columns:
        # Fill NAs with 'kids' and rename the column
        df[gender_col] = df[gender_col].fillna("kids")
        df = df.rename(columns={gender_col: "Pohlaví"})

    # Process Velikost column to create Velikost [EUR]
    if "Velikost" in df.columns:

        def convert_size(size_str):
            if pd.isna(size_str):
                return None

            # Remove 'EUR ' prefix and strip whitespace
            size_str = str(size_str).replace("EUR ", "").strip()

            # Handle decimal values (e.g., '44,5' or '44.5')
            if any(c in size_str for c in (",", ".")):
                if "," in size_str:
                    size_str = size_str.replace(",", ".")
                try:
                    return float(size_str)
                except (ValueError, TypeError):
                    return None

            # Handle fractions (e.g., '44 2/3')
            if " " in size_str and "/" in size_str:
                whole_part, fraction = size_str.split(" ", 1)
                try:
                    # Convert fraction to decimal
                    num, denom = map(int, fraction.split("/"))
                    decimal = num / denom
                    return float(whole_part) + decimal
                except (ValueError, ZeroDivisionError):
                    try:
                        return float(whole_part)
                    except (ValueError, TypeError):
                        return None

            # Handle simple integer values
            try:
                return float(size_str)
            except (ValueError, TypeError):
                return None

        df["Velikost [EUR]"] = df["Velikost"].apply(convert_size)

    # Drop columns
    df = df.drop(
        columns=[
            "Hmotnost boty - ženy",
            "Hmotnost boty - muži",
            "description",
            "{http://base.google.com/ns/1.0}size",
            "product_type",
            "Technologie",
            "{http://base.google.com/ns/1.0}age_group",
            "Materiál střední části",
        ],
        errors="ignore",
    )

    # Remove gender prefixes from title and group_title columns
    gender_prefixes = ["Pánská tenisová obuv ", "Dámská tenisová obuv ", "Dětská tenisová obuv "]

    for col in ["title", "group_title"]:
        if col in df.columns:
            # Remove gender prefixes
            for prefix in gender_prefixes:
                df[col] = df[col].str.replace(prefix, "", regex=False)

    # Remove everything after 'EUR' (including 'EUR') in group_title
    if "group_title" in df.columns:
        df["group_title"] = df["group_title"].str.replace(r"\s*EUR.*$", "", regex=True).str.rstrip()

    # Add brand prefix to Modelová řada
    if "Modelová řada" in df.columns and "brand" in df.columns:
        df["Modelová řada"] = df["brand"] + " - " + df["Modelová řada"].astype(str)

    # Rename columns
    df = df.rename(
        columns={
            "Doporučený povrch": "Doporučený povrch hřiště",
            "Svršek": "Materiál svršku",
            "Kolekce": "Turnajová kolekce",
        },
        errors="ignore",
    )

    return df
