import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations. INSERT THE CODE BELOW"""

    # Drop specified columns if they exist
    columns_to_drop = [
        "{http://base.google.com/ns/1.0}gtin",
        "stock",
        "rozmery_baleni",
        "{http://base.google.com/ns/1.0}custom_label_4",
        "delivery_date",
        "product_length",
        "product_width",
        "product_height",
        "shipping_length",
        "shipping_width",
        "shipping_height",
        "shipping_weight",
        "rozmery_produktu",
        "{http://base.google.com/ns/1.0}ads_grouping",
        "{http://base.google.com/ns/1.0}sale_price",
        "{http://base.google.com/ns/1.0}additional_image_link",
    ]

    # Only drop columns that exist in the DataFrame
    columns_to_drop = [col for col in columns_to_drop if col in df.columns]
    df = df.drop(columns=columns_to_drop)

    # Rename columns
    rename_columns = {
        "{http://base.google.com/ns/1.0}description": "description",
        "Doporučený povrch": "Povrch hřiště",
        "Doporučené použití": "player_level",
    }
    df = df.rename(columns={k: v for k, v in rename_columns.items() if k in df.columns})

    # Process 'Balení míčků' column if it exists
    if "Balení míčků" in df.columns:
        # Convert values like '2x4 ks' to numerical values
        df["Balení míčků"] = df["Balení míčků"].apply(
            lambda x: str(eval(x.replace(" ks", "").replace("x", "*"))) if isinstance(x, str) and "x" in x else x
        )
        # Extract numbers and convert to float
        df["Balení míčků"] = df["Balení míčků"].astype(str).str.extract(r"(\d+)", expand=False).astype(float)
        # Fill missing values with 1 and convert to int
        df["Balení míčků"] = df["Balení míčků"].fillna(1).astype(int)

    # Fill missing values in 'Typ míče' column if it exists
    if "Typ míče" in df.columns:
        df["Typ míče"] = df["Typ míče"].fillna("velké")

    # Calculate unit price if both 'price_czk' and 'Balení míčků' columns exist
    if all(col in df.columns for col in ["price_czk", "Balení míčků"]):
        df["unit_price"] = (df["price_czk"] / df["Balení míčků"]).round(1)

    df.drop_duplicates(subset=["title"], keep="first", inplace=True)

    # Clean up title and group_title columns for tennis balls
    if "title" in df.columns:
        df["title"] = df["title"].str.replace("Tenisové míče ", "").str.replace("Dětské tenisové míče ", "").str.replace("  ", " ")
        df["title"] = df["title"].str.replace("Tenisový míč ", "").str.replace("Dětský tenisový míč ", "").str.replace("  ", " ")

    if "group_title" in df.columns:
        df["group_title"] = df["group_title"].fillna(df.get("title", ""))  # Fill with title if group_title is None
        df["group_title"] = (
            df["group_title"].str.replace("Tenisové míče ", "").str.replace("Dětské tenisové míče ", "").str.replace("  ", " ")
        )
        df["group_title"] = (
            df["group_title"].str.replace("Tenisový míč ", "").str.replace("Dětský tenisový míč ", "").str.replace("  ", " ")
        )

    return df


# DO NOT CHANGE CODE BELOW
