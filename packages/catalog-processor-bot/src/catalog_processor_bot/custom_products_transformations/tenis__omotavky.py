import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations. INSERT THE CODE BELOW"""

    # 1. Delete description column if it exists
    if "description" in df.columns:
        df = df.drop(columns=["description"])

    # 2. Rename 'Hlavní výhoda omotávky' to 'Vlastnosti omotávky' if it exists
    if "Hlavní výhoda omotávky" in df.columns:
        df = df.rename(columns={"Hlavní výhoda omotávky": "Vlastnosti omotávky"})

    # 2. Clean title and group_title columns
    for col in ["title", "group_title"]:
        if col in df.columns:
            df[col] = df[col].str.replace(r"^(Vrchní omotávka|Základní omotávka)\s*", "", regex=True).str.strip()

    # 3. Trim "Vhodnost použití sqash" column
    if "Vhodnost použití squash" in df.columns:
        df["Vhodnost použití squash"] = df["Vhodnost použití squash"].str.replace("ano - velmi doporučujeme", "ano")

    # 4. Delete "product_type" column
    if "product_type" in df.columns:
        df = df.drop(columns=["product_type"])

    # 5. Process "Počet vrstev omotávky" column
    if "Počet vrstev omotávky" in df.columns:
        df["Počet vrstev omotávky"] = df["Počet vrstev omotávky"].str.extract(r"(\d+)").astype("float64")

    # 6. Process "Tloušťka omotávky" column
    if "Tloušťka omotávky" in df.columns:
        # Replace "není uvedeno" with NaN
        df["Tloušťka omotávky"] = df["Tloušťka omotávky"].replace("není uvedeno", pd.NA)
        # Extract numeric values and convert to float
        df["Tloušťka omotávky"] = df["Tloušťka omotávky"].str.extract(r"([\d.]+)").astype("float64")
        # Rename the column
        df = df.rename(columns={"Tloušťka omotávky": "Tloušťka omotávky [mm]"})

    # 7. Delete "Vhodnost použití tenis" column
    if "Vhodnost použití tenis" in df.columns:
        df = df.drop(columns=["Vhodnost použití tenis"])

    # 8. Process "Úroveň absorbce vlhkosti" column
    if "Úroveň absorbce vlhkosti" in df.columns:
        # Extract numeric values, remove decimal part if it's .0
        df["Úroveň absorbce vlhkosti"] = df["Úroveň absorbce vlhkosti"].str.extract(r"(\d+)(?:\.0+)?")
        # Convert to numeric, non-numeric become NaN
        df["Úroveň absorbce vlhkosti"] = pd.to_numeric(df["Úroveň absorbce vlhkosti"], errors="coerce")
        # Rename the column
        df = df.rename(columns={"Úroveň absorbce vlhkosti": "Úroveň absorbce vlhkosti [%]"})

    # End of space where AI can enter code.
    return df
