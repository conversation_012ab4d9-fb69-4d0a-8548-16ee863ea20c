import numpy as np
import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations."""
    # Clean and transform the data

    df.rename(
        columns={
            "min_age": "age_min",
            "max_age": "age_max",
            "Barva": "barva",
            "Velikost hlavy [cm²]": "velikost_hlavy_cm2",
            "Délka tenisové rakety [mm]": "delka",
            "Tuhost rámu [RA]": "Tuhost rámu",
            "Vyvážení tenisové rakety [mm]": "vyvazeni",
        },
        inplace=True,
    )

    # Drop columns
    columns_to_drop = ["{http://base.google.com/ns/1.0}google_product_category", "{http://base.google.com/ns/1.0}is_bundle"]
    df = df.drop(columns=[col for col in columns_to_drop if col in df.columns])

    df["Modelová řada"] = df["brand"] + " " + df["Modelová řada"]
    df["hmotnost"] = df["Hmotnost tenisové rakety"]
    df["velikost_hlavy_cm2"] = df["velikost_hlavy_cm2"].fillna(0).astype(float).astype(int)
    df["velikost_hlavy_in2"] = (df["velikost_hlavy_cm2"] * 0.155).round().astype(int)
    df["Kategorie délky rakety"] = df["Kategorie délky rakety"].str.replace("(", "").str.replace(")", "")
    df["Výška hráče"] = df["Výška hráče"].str.replace("–", "-")
    df["vyska_hrace_min"] = df["Výška hráče"].str.split("-").str[0]
    df["vyska_hrace_max"] = df["Výška hráče"].str.split("-").str[1]
    df["vyska_hrace_min"] = df["vyska_hrace_min"].str.extract(r"(\d+)")
    df["vyska_hrace_max"] = df["vyska_hrace_max"].str.extract(r"(\d+)")
    df.rename(columns={"Tuhost rámu:": "Tuhost rámu"}, inplace=True)
    df.rename(columns={"product_type": "player_type"}, inplace=True)
    df = df[~df.player_type.str.contains("ženy", na=False)]
    df = df.copy()

    # Deal with vzor výpletu column
    if "Vzor výpletu" in df.columns:

        def extract_dimension(value, index):
            if pd.isna(value):
                return np.nan
            parts = str(value).split("x")
            if len(parts) <= index:
                return np.nan
            try:
                return float(parts[index])
            except (ValueError, TypeError):
                return np.nan

        df["Vzor výpletu X"] = df["Vzor výpletu"].apply(lambda x: extract_dimension(x, 0))
        df["Vzor výpletu Y"] = df["Vzor výpletu"].apply(lambda x: extract_dimension(x, 1))

    df["Velikost gripu"] = df["Velikost gripu"].str.replace(r"\(.*?\)", "", regex=True).str.strip()

    # Drop description
    if "description" in df.columns:
        df = df.drop(columns=["description"])

    # Remove prefixes
    df["title"] = df["title"].str.replace("Tenisová raketa ", "").str.replace("Dětská tenisová raketa ", "")
    df["group_title"] = df["group_title"].str.replace("Tenisová raketa ", "").str.replace("Dětská tenisová raketa ", "")

    # Delete Grip from group_title
    df["group_title"] = df["group_title"].str.replace(r"\s*L\d+\b", "", regex=True)

    # Clean 'Hmotnostní třída' column - remove parentheses and extra spaces
    if "Hmotnostní třída" in df.columns:
        df["Hmotnostní třída"] = df["Hmotnostní třída"].str.replace(r"[()]", "", regex=True)  # Remove parentheses
        df["Hmotnostní třída"] = df["Hmotnostní třída"].str.replace(r"\s+", " ", regex=True)  # Replace multiple spaces with single space
        df["Hmotnostní třída"] = df["Hmotnostní třída"].str.strip()  # Remove leading/trailing spaces

    df.drop_duplicates(inplace=True)

    cols = [
        "group_title",
        "title",
        "availability",
        "brand",
        "barva",
        "image_link",
        "image_link_master",
        "item_group_id",
        "material",
        "price_czk",
        "player_type",
        "id",
        "link",
        "link_master",
        "Velikost gripu",
        "Vypletená raketa",
        "Kategorie délky rakety",
        "Vzor výpletu",
        "Roční kolekce",
        "Modelová řada",
        "Použitá základní omotávka",
        "Používá",
        "Švihová hmotnost (s výpletem)",
        "Tuhost rámu",
        "Technologie",
        "rank",
        "hmotnost",
        "Hmotnostní třída",
        "vyvazeni",
        "velikost_hlavy_cm2",
        "velikost_hlavy_in2",
        "delka",
        "age_min",
        "age_max",
        "vyska_hrace_min",
        "vyska_hrace_max",
        "Vzor výpletu X",
        "Vzor výpletu Y",
    ]

    df = df[cols]

    return df


# DO NOT CHANGE CODE BELOW
