import re

import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process size information and clean up product data"""
    # Drop description column if it exists
    if "description" in df.columns:
        df = df.drop(columns=["description"])

    # Remove prefixes from 'title' and 'group_title' columns
    prefixes_to_remove = ["Vložky do bot", "Podpatěnky"]

    for col in ["title", "group_title"]:
        if col in df.columns:
            for prefix in prefixes_to_remove:
                # Remove prefix followed by optional whitespace and hyphen/colon if present
                df[col] = df[col].str.replace(f"^{re.escape(prefix)}[\\s\\-:]*", "", regex=True, case=False)

    # Clean up group_title: remove EUR numbers and size suffixes (S, M, L, XL, L/XL, etc.)
    if "group_title" in df.columns:
        # Remove EUR followed by numbers (with optional decimal point or range like 45-46)
        df["group_title"] = df["group_title"].str.replace(r"\s*EUR\s*\d+([\-\.]\d+)?\s*$", "", regex=True)
        # Handle all size suffix patterns including S, M, L, XL, S/, L/X, etc.
        df["group_title"] = df["group_title"].str.replace(r"\s*[SMLX]+(?:\s*\/\s*[SMLX]+)?\s*$", "", regex=True, flags=re.IGNORECASE)
        # Trim any extra whitespace that might be left
        df["group_title"] = df["group_title"].str.strip()

    # Simplified size handling - directly create min and max EUR columns
    if "Velikost" in df.columns:
        # Extract size letters (S, M, L, XL) for mapping
        size_letters = df["Velikost"].str.extract(r"([SML]|XL)", flags=re.IGNORECASE).squeeze().str.upper()

        # Extract numeric values for direct size values
        numeric_sizes = df["Velikost"].str.extract(r"(\d+(?:\.\d+)?)")

        # Define size mapping for S/M/L/XL to numeric ranges
        size_mapping = {"S": (35.0, 38.0), "M": (39.0, 41.0), "L": (42.0, 45.0), "XL": (42.0, 45.0)}

        # Initialize min/max columns
        df["Velikost min [EUR]"] = pd.to_numeric(numeric_sizes[0], errors="coerce")
        df["Velikost max [EUR]"] = df["Velikost min [EUR]"]  # For single values, min=max

        # Apply mappings for S/M/L/XL values
        for size, (min_val, max_val) in size_mapping.items():
            mask = size_letters == size
            df.loc[mask, "Velikost min [EUR]"] = min_val
            df.loc[mask, "Velikost max [EUR]"] = max_val

        # Drop the original Velikost column
        df = df.drop(columns=["Velikost"])

        # Also drop the Google size column if it exists
        size_col = "{http://base.google.com/ns/1.0}size"
        if size_col in df.columns:
            df = df.drop(columns=[size_col])

    df.group_title = df.group_title.str.replace(r"EUR\s*", "", regex=True)

    df.drop(columns=["product_type", "custom_label_0"], inplace=True)

    return df
