import pandas as pd


def process(df: pd.DataFrame) -> pd.DataFrame:
    """Process the DataFrame with custom transformations. INSERT THE CODE BELOW"""

    if "title" in df.columns:
        df["title"] = df["title"].str.replace("Vibrastop", "", regex=False)
    if "group_title" in df.columns:
        df["group_title"] = df["group_title"].str.replace("Vibrastop", "", regex=False)
    if "Modelová řada" in df.columns:
        df = df.drop(columns=["Modelová řada"])

    return df


# DO NOT CHANGE CODE BELOW
