import argparse
import re
import sys
from pathlib import Path
from typing import Tuple, Optional, Dict
import os

import loguru
import numpy as np
import pandas as pd
from unidecode import unidecode


def filter_by_valid_product_types(df, product_types, logger):
    # Convert product types to lowercase for case-insensitive comparison
    df["product_type_lower"] = df["product_type"].str.lower()

    # Filter based on category type
    if isinstance(product_types, str):
        categories = [product_types.lower()]
    else:  # It's a list
        categories = [str(cat).lower() for cat in product_types]

    # Check if any of the requested categories exist
    existing_categories = set(df["product_type_lower"].dropna().unique())
    missing_categories = [cat for cat in categories if cat not in existing_categories]
    valid_categories = [cat for cat in categories if cat in existing_categories]

    if missing_categories:
        logger.warning(f"The following categories were not found in the data: {', '.join(missing_categories)}." f"Skipping them.")

    df = df[df["product_type_lower"].isin(valid_categories)].drop(columns=["product_type_lower"])
    return df

def filter_by_general_filter(df: pd.DataFrame, general_filter: Dict[str, str]):

    pick = pd.Series([True] * len(df), index=df.index)

    for col, filter_by in general_filter.items():
        pick = pick & df[col].str.lower().str.contains(filter_by.lower())

    return df[pick]


def preprocess_catalog(*, safe_domain_name, product_types, general_filter, logger=loguru.logger, dataframe=None):
    # Create data directory if it doesn't exist

    # Read the data if no DataFrame is provided
    if dataframe is None:
        data_path = Path("data")
        data_path.mkdir(parents=True, exist_ok=True)

        # Define input and output paths
        input_file = data_path / "catalog_all_products.csv"

        # Handle both string and list inputs for category
        output_file = data_path / f"catalog_{safe_domain_name}_raw.csv"

        df = pd.read_csv(input_file)
    else:
        df = dataframe

    # Filter the dataframe
    if product_types:
        df = filter_by_valid_product_types(df, product_types, logger=logger)
    elif general_filter:
        df = filter_by_general_filter(df, general_filter)
    else:
        raise Exception("either product_types or general_filter must not be null.")

    # Drop all none columns and columns with only one unique value (except for 'availability')
    df = df.dropna(axis=1, how="all")
    df = df.loc[:, (df.nunique() > 1) | (df.columns == "availability") | (df.columns == "brand" )]

    # Apply data preprocessing
    df = preprocess_dataframe(df)

    if dataframe is None:
        # Save the processed data
        df.to_csv(output_file, index=False)
        print(f"Preprocessed data saved to {output_file.absolute()}")

    return df


def strip_common_prefix_from_series(series):
    col_values = series.dropna().astype(str).tolist()

    if not col_values:
        return series

    prefix = str(os.path.commonprefix(col_values))
    if not prefix:
        return series

    return series.astype(str).str.replace(f'^{re.escape(prefix)}', '', regex=True)

def fix_group_title(df):
    df2 = df[["item_group_id", "group_title"]].groupby(["item_group_id"]).agg(lambda x: os.path.commonprefix(list(x)))
    df = pd.merge(df, df2, how="left", left_on="item_group_id", right_on="item_group_id")
    df["group_title"] = df["group_title_y"]
    df = df.drop(columns=["group_title_y", "group_title_x"])
    return df

def preprocess_dataframe(df):
    """
    Apply preprocessing steps to the DataFrame.

    Args:
        df (pd.DataFrame): Input DataFrame to be processed

    Returns:
        pd.DataFrame: Processed DataFrame
    """

    # create new columns if they don't exist
    if "item_group_id" not in df.columns and "id" in df.columns:
        df["item_group_id"] = df["id"]
    if "link_master" not in df.columns and "link" in df.columns:
        df["link_master"] = df["link"]
    if "image_link_master" not in df.columns and "image_link" in df.columns:
        df["image_link_master"] = df["image_link"]

    df["link_master"] = df["link_master"].fillna(df["link"])
    df["image_link_master"] = df["image_link_master"].fillna(df["image_link"])
    df["item_group_id"] = df["item_group_id"].fillna(df["id"])

    # Manage the description column
    df = df.drop(columns="description") if "description" in df.columns else df
    # Rename the description column only if it exists
    if "{http://base.google.com/ns/1.0}description" in df.columns:
        df = df.rename(columns={"{http://base.google.com/ns/1.0}description": "description"})

    # Wipe namespaces
    df.columns = [col.split('}')[-1] if '}' in col else col for col in df.columns]

    # convert id and item_group_id from int to string, but before turning them from float to str,
    # make sure you omit the .0 after numbers
    for col in ["id", "item_group_id"]:
        if col in df.columns:
            df[col] = df[col].apply(
                lambda x: (
                    str(int(x))
                    if pd.notnull(x) and (isinstance(x, float) or str(x).replace(".", "").isdigit())
                    else str(x) if pd.notnull(x) else x
                )
            )

    # Process price_czk if it exists
    if "price_czk" in df.columns and df["price_czk"].dtype == "object":
        df["price_czk"] = df["price_czk"].astype(str).str.replace(r"\D", "", regex=True)
        df = df[df["price_czk"] != ""]  # Remove rows where price becomes empty after cleaning
        df["price_czk"] = df["price_czk"].astype(int)

    # Availability unification
    if "availability" in df.columns:
        df["availability"] = df["availability"].str.replace("in stock", "in_stock", case=False)
        df["availability"] = df["availability"].str.replace("out of stock", "out_of_stock", case=False)

    # deal with age column
    if any(col for col in df.columns if "věk" in col.lower()):

        def extract_age_range(age_str):
            if pd.isna(age_str):
                return np.nan, np.nan
            match = re.match(r"(\d+)-(\d+)\s*let", str(age_str))
            if match:
                min_age = int(match.group(1))
                max_age = int(match.group(2))
                return min_age, max_age
            else:
                return np.nan, np.nan

        age_column = next(col for col in df.columns if "věk" in col.lower())
        age_tuples = df[age_column].apply(extract_age_range)
        df["min_age"] = age_tuples.apply(lambda x: x[0] if isinstance(x, tuple) else np.nan)
        df["max_age"] = age_tuples.apply(lambda x: x[1] if isinstance(x, tuple) else np.nan)

    # drop products with same title
    if "title" in df.columns:
        df = df.drop_duplicates(subset=["title"], keep="first")

    # if there are columns with string 'hmotnost' but not 'hmotnostní' then drop the nonnumeric values
    for col in df.columns:
        if "hmotnost" in col.lower() and "hmotnostní" not in col.lower() and df[col].dtype == "object":
            df[col] = pd.to_numeric(df[col].astype(str).str.replace(r"[^\d]", "", regex=True), errors="coerce")

    # Handle color columns
    if "Barva" in df.columns and "color" in df.columns:
        df.drop(columns=["color"], inplace=True)

    if "Barva" in df.columns and df["Barva"].dtype == "object":
        df["Barva"] = df["Barva"].astype(str).str.replace(",", "/")

    if "Materiál" in df.columns and "material" in df.columns:
        df.drop(columns=["Materiál"], inplace=True)

    # Clean up title
    if "title" in df.columns:
        df["title"] = df["title"].str.replace(r"[\(\)]", "", regex=True)
        df["title"] = df["title"].str.replace(r"(\d),(\d)", r"\1.\2", regex=True)
        df["title"] = df["title"].str.replace(",", "", regex=True)
        df["title"] = df["title"].str.replace(r"\s+", " ", regex=True)
        df["title"] = df["title"].str.strip()

        df["group_title"] = df["title"]

    # Process units in columns
    df = process_units_columns(df)

    if "item_group_id" in df.columns and  "group_title" in df.columns:
        df = fix_group_title(df)

    return df


# def extract_multiple_value_units(text: str) -> Optional[Tuple[float, str, float, str]]:
#     """
#     Extract two value-unit pairs from a string in format 'value1 unit1, value2 unit2'.
#     If unit2 is not provided, it will use unit1.
#
#     Returns:
#         Tuple of (value1, unit1, value2, unit2) or None if no match is found.
#     """
#     if not isinstance(text, str):
#         return None
#
#     pattern = r'(\d+[.,]?\d*)\s*([a-zA-Z]+)\s*[,]?\s*[+]?\s*(\d+[.,]?\d*)\s*([a-zA-Z]*)'
#     match = re.match(pattern, text.strip())
#
#     if not match:
#         return None
#
#     # Handle both decimal separators by replacing comma with dot
#     value1 = match.group(1).replace(',', '.')
#     unit1 = match.group(2).lower()
#     value2 = match.group(3).replace(',', '.')
#     unit2 = (match.group(4) or unit1).lower()
#
#     # Check if units match (case-insensitive)
#     if unit1 != unit2:
#         return None
#
#     return float(value1), unit1, float(value2), unit2


def extract_units_and_convert_to_float(text: str) -> Tuple[Optional[float], Optional[str]]:
    """
    Extract numeric value and units from a string.
    Returns (float_value, units) or (None, None) if no match is found.
    """
    if not isinstance(text, str):
        return None, None

    # Pattern to match numbers with optional decimal point/comma and units (including superscripts)
    pattern = r"^\s*([-+]?\d*[.,]?\d+)\s*([a-zA-Zµ°%²³]+)\s*$"
    match = re.match(pattern, str(text).strip())
    if not match:
        return None, None

    value_str, units = match.groups()
    # Replace comma with dot for float conversion
    value = float(value_str.replace(",", "."))
    return value, units


def process_units_columns(df: pd.DataFrame) -> pd.DataFrame:
    """
    Process DataFrame columns to extract units from values and update column names.
    Handles both single value-unit pairs and multiple value-unit pairs.
    Returns a new DataFrame with processed columns.
    """
    df_processed = df.copy()
    processed_columns = set()

    for col in df_processed.select_dtypes(include=["object"]).columns:
        if col in processed_columns or re.search(r"\[.*\]$", col):
            continue

        # # First check for multiple value-unit pairs (e.g., "100g, 200g")
        # can_convert_multiple = True
        # units_found = set()
        #
        # for val in df_processed[col]:
        #     if pd.isna(val):
        #         continue
        #
        #     result = extract_multiple_value_units(str(val).strip())
        #     if result is None:
        #         can_convert_multiple = False
        #         break
        #
        #     _, unit1, _, unit2 = result
        #     units_found.update([unit1, unit2])
        #
        #     if len(units_found) > 1:
        #         can_convert_multiple = False
        #         break
        #
        # if can_convert_multiple and units_found:
        #     unit = units_found.pop()
        #     x_col = f"{col} X {unit}"
        #     y_col = f"{col} Y {unit}"
        #     old_col = f"{col} {unit}"
        #
        #     # Create new columns with extracted values
        #     df_processed[x_col] = df_processed[col].apply(
        #         lambda x: extract_multiple_value_units(str(x).strip())[0]
        #         if pd.notnull(x) else x
        #     )
        #     df_processed[y_col] = df_processed[col].apply(
        #         lambda x: extract_multiple_value_units(str(x).strip())[2]
        #         if pd.notnull(x) else x
        #     )
        #
        #     # Rename original column
        #     df_processed.rename(columns={col: old_col}, inplace=True)
        #     processed_columns.update([x_col, y_col, old_col])
        #     continue

        # If not multiple value-units, try single value-unit
        can_convert_single = True
        units_found = set()

        for val in df_processed[col]:
            if pd.isna(val):
                continue

            value, units = extract_units_and_convert_to_float(str(val).strip())
            if value is None or units is None:
                can_convert_single = False
                break

            units_found.add(units)

            if len(units_found) > 1:
                can_convert_single = False
                break

        if can_convert_single and units_found:
            units = units_found.pop()
            new_col_name = f"{col} [{units}]"

            df_processed[new_col_name] = df_processed[col].apply(
                lambda x: extract_units_and_convert_to_float(str(x).strip())[0] if pd.notnull(x) else x
            )

            if new_col_name != col:
                df_processed.drop(columns=[col], inplace=True)

            processed_columns.add(new_col_name)

    return df_processed


if __name__ == "__main__":

    import dotenv
    dotenv.load_dotenv()

    parser = argparse.ArgumentParser(description="Preprocess catalog data for a specific category.")
    parser.add_argument(
        "--category", nargs="+", required=True, help="Category or list of categories to filter products by (case-insensitive)"
    )

    args = parser.parse_args()

    try:
        df = preprocess_catalog(args.category)
        print(f"Successfully processed {len(df)} products.")
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
