import pandas as pd


def clean_column_of_spaces(df: pd.DataFrame) -> pd.DataFrame:
    columns_to_clean = []
    if "title" in df.columns:
        columns_to_clean.append("title")
    if "group_title" in df.columns:
        columns_to_clean.append("group_title")

    for col in columns_to_clean:
        df[col] = df[col].str.replace(r"[\(\)]", "", regex=True)
        df[col] = df[col].str.replace(r"(\d),(\d)", r"\1.\2", regex=True)
        df[col] = df[col].str.replace(",", "", regex=True)
        df[col] = df[col].str.replace(r"\s+", " ", regex=True)
        df[col] = df[col].str.strip()

    return df



def convert_numeric_columns_to_int_floats(df: pd.DataFrame) -> pd.DataFrame:
    """Convert numeric columns to appropriate types (int or float)."""

    df = df.copy()

    skip_columns = {"id", "item_group_id"}

    for col in df.columns:
        if col in skip_columns:
            continue

        # If coercion created new nulls, then column contained non-numeric values, so skip
        original_nulls = df[col].isnull()
        numerical_col = pd.to_numeric(df[col], errors="coerce")
        if not original_nulls.equals(numerical_col.isnull()):
            continue

        try:
            if (numerical_col.dropna() % 1 == 0).all():
                df[col] = numerical_col.astype("Int64")
            else:
                df[col] = numerical_col.astype("Float64")

        except (ValueError, TypeError):
            # Skip this column if any issue occurs during conversion
            continue

    return df
