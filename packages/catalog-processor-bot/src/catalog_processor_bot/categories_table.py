from typing import List

from sqlalchemy import <PERSON><PERSON><PERSON>, BigInteger, Text, text
from sqlalchemy import create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

# Define SQLAlchemy model for catalog_categories table
Base = declarative_base()


class CatalogCategory(Base):
    __tablename__ = "catalog_categories"

    id = Column(BigInteger, primary_key=True)
    name = Column(Text, nullable=False)
    catalog_json = Column(Text, nullable=False)
    product_types = Column(Text)  # Will store string representation of a list

    def __repr__(self):
        return f"<CatalogCategory(id={self.id}, name='{self.name}')>"


def get_all_categories(connection_string: str) -> List[str]:
    """Get all category names from the database."""
    engine = create_engine(connection_string)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        categories = session.query(CatalogCategory.name).all()
        return [category[0] for category in categories]
    except Exception as e:
        print(f"Error retrieving categories: {e}")
        return []
    finally:
        session.close()
        engine.dispose()


def check_if_domain_exists(domain_name: str, connection_string: str) -> bool:
    """Check if the domain exists in the database."""
    engine = create_engine(connection_string)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        domain = session.query(CatalogCategory).filter(CatalogCategory.name == domain_name).first()
        return bool(domain)
    except Exception as e:
        print(f"Error checking domain: {e}")
        return False
    finally:
        session.close()
        engine.dispose()


def _get_category_id(connection_string: str, domain_name: str) -> int | None:
    """Get the catalog category ID for the given domain name."""
    if not domain_name:
        return None

    try:
        engine = create_engine(connection_string)
        with engine.connect() as conn:
            result = conn.execute(text("SELECT id FROM catalog_categories WHERE name = :name"), {"name": domain_name}).fetchone()

            if not result:
                raise ValueError(f"Category '{domain_name}' not found in catalog_categories table")

            return result[0]
    except Exception as e:
        print(f"Error fetching catalog category ID: {e}")
        raise
