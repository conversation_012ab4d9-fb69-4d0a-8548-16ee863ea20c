domain_settings:
  ortézy:
    category_type: table
    products_table: products_ortezy
    product_name: ortezy
    optional_title_prefix: ''
    search_columns: title,availability,price_czk,{http://base.google.com/ns/1.0}custom_label_0,brand,product_type,Typ
      produktu,Vhodnost pro sporty,Barva,material,Umístění,{http://base.google.com/ns/1.0}size,Velikost,Počet
      ks v balení,Hodnocení Sportega,Hodnocení komfortu,Stupeň podpory,Typ zranění,Technologie,Hodnocení
      použité technologie,Šířka [cm],<PERSON><PERSON>lk<PERSON> [cm],stupen podpory_float
    fetch_columns: availability,price_czk,{http://base.google.com/ns/1.0}custom_label_0,brand,product_type,Typ
      produktu,Vhodnost pro sporty,Umístění,{http://base.google.com/ns/1.0}size,Velikost,Počet
      ks v balení,Hodnocení Sportega,Hodnocení komfortu,Stupeň podpory,Typ zranění,Technologie,Hodnocení
      použité technologie,Šířka [cm],Délka [cm],stupen podpory_float,description
    color_column: Barva
    variant_column: null
    categorical_columns: availability,{http://base.google.com/ns/1.0}custom_label_0,Stupeň
      podpory
    query_responser_llm: gpt-4.1-2025-04-14
    table_instructions: Use czech names of colors for column `barva`.If the question
      contains combination of colors, use `&` operator for individual colors in colors
      subcondition.If the question contains words like big, high, large, small, low,
      cheap, expensive, and such for other paremeters than weight, search for lowest
      or highes 30 % values of that parameters.If the question contains words like
      biggest, highest, largest, smallest, lowest, cheapest, most expensive and such
      for other parameters than weight, search for minimal or maximal values of that
      parameter.Do not recommend child ortezy, unless you know the user is talking
      about a child.
    agent_instructions: You have access to product catalog of Sportega company that
      contains all the possible information about ortezy.In the Final answer, you
      must tell whether the user´s question violates the recommended products descriptions.
    npf_config:
      variant_column_str: null
      split_column_config: null
      weights_override: null
      categorical_cols:
      - availability
      - '{http://base.google.com/ns/1.0}custom_label_0'
      - Stupeň podpory
      ordinal_mappings: null
      metacategories: null
