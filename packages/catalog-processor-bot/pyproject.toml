[project]
name = "catalog-processor-bot"
version = "0.1.15"
description = "Local tool for processing catalog products."
authors = [
    { name = "<PERSON>", email = "<EMAIL>" }
]
dependencies = [
    "numpy>=2.0.2",
    "pandas>=2.2.3",
    "psycopg2-binary>=2.9.10",
    "pyyaml>=6.0.0",
    "pgvector>=0.4.1",
    "sqlalchemy>=2.0.38",
    "langchain-openai>=0.3.18",
    "langchain-google-genai>=2.1.4",
    "langchain>=0.3.25",
    "python-dotenv>=1.1.0",
    "pystemmer>=3.0.0",
    "bm25s>=0.2.13",
    "boto3>=1.38.36",
    "diskcache>=5.6.3",
    "platformdirs>=4.3.8",
    "loguru>=0.7.3",
    "humanize>=4.12.3",
    "unidecode>=1.4.0",
]
requires-python = ">= 3.10"

[build-system]
requires = ["hatchling", "toml"]
build-backend = "hatchling.build"

[tool.uv]
managed = true

[dependency-groups]
dev = [
    "pandas-stubs==2.3.0.250703",
    "boto3-stubs==1.39.9",
]

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build]
exclude = [
    ".venv",
    ".windsurf",
    "data",
    "config",
    ".cache",
    "dist",
]

[tool.hatch.build.targets.wheel]
packages = ["src/catalog_processor_bot"]
