domain_settings:
  "tenis > product": # Product name in format "tenis > sub_product"
    products_table: productname_products
    embeddings_table: productname_embeddings_binary_live
    descriptions_table: productname_descriptions (but only if the column "description" is present, else null)
    product_name: product_name
    optional_title_prefix: "leave empty"
    search_columns: title,availability,price_czk, # + write all columns separated by commas. There is no space after comma.
    fetch_columns: # write all columns separated by commas. There is no space after comma. Exclude column "rank"
    color_column: name_of_color_column # name of column that contains color
    variant_column: column_name # name of column that is in a title, but excluded from group_title
    categorical_columns: categorical_columns # list of all columns that are categorical
    table_instructions: >- #non-newline
      Here write the instructions for the AI agent, how to search in the product catalog.
    agent_instructions: >- #non-newline
      Instructions for AI agent
    npf_config:
      variant_column_str: "column_name"  # name of column that is in a title, but excluded from group_title
      # Configuration for columns that need to be split into multiple components
      split_column_config:
        "column_name": # Example: A pattern that needs to be split into X and Y components
          - "column X"  # X component
          - "column Y"  # Y component

      # write down weights for column that are not that important
      weights_override:
        "column_name": 0.5
        "column_name_2": 0.5

      # List of categorical columns
      categorical_cols:
        - "cat_col_1"
        - "cat_col_2"

      # Here write catagorical columns and give them ordinal mapping values. Only for columns, where it makes sense
      ordinal_mappings:

        "column_name_1":
          "value_1": 0
          "value_2": 1
          "value_3": 2

        "column_name_2":
          "value_1": 0
          "value_2": 1

      # Categorical columns that are metacategories, and their values can never mix.
      metacategories:
        "column_name_1":
          "value_1": 1
          "value_2": 1
          "value_3": 2
