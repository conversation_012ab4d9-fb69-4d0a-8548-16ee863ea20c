workflow:
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "stage" || $CI_COMMIT_BRANCH == "prod"'


stages:
  - build
  - deploy
  - test

variables:
  DOCKER_REGISTRY: "************.dkr.ecr.eu-central-1.amazonaws.com/private-catalog-postprocess"
  REPOSITORY_NAME: "private-catalog-postprocess"
  SERVICE_NAME: "catalog-postprocess"

  AWS_ACCOUNT_ID: "************"
  AWS_ACCESS_KEY_ID: "********************"
  AWS_SECRET_ACCESS_KEY: "hrfbk25/DQXH+7VGEIn1F2HNAc/hnqgS0sWn9xeH"
  AWS_DEFAULT_REGION: "eu-central-1"
  
  S3_BUCKET: "lambda-deployment-packages-sportega"
  OPENAI_API_KEY: "*******************************************************************************************************************************************"
  GOOGLE_API_KEY: "AIzaSyDjEYajdEO5sK29D5rMTSR-JoIDPIMpOJ8"
  GITLAB_PERSONAL_ACCESS_TOKEN: "**************************"

build:
  stage: build
  image:
    name: richard1robosales1ai/docker-dind-awscli:latest
  services:
    - docker:dind
  script:
    - export TRUNKVER=$(trunkver generate --build-ref $CI_JOB_ID --source-ref $CI_COMMIT_SHA)
    - echo "Building at $(date -u '+%Y-%m-%dT%H:%M:%SZ')" 
    - docker build --provenance=false . -t "$DOCKER_REGISTRY:$CI_COMMIT_SHA"
    - aws ecr get-login-password | docker login --username AWS --password-stdin $DOCKER_REGISTRY
    - docker tag $DOCKER_REGISTRY:$CI_COMMIT_SHA $DOCKER_REGISTRY:$CI_COMMIT_BRANCH
    - docker push $DOCKER_REGISTRY:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY:$CI_COMMIT_BRANCH
    - aws lambda update-function-code --function-name ${CI_COMMIT_BRANCH}_post_importer --image-uri ************.dkr.ecr.eu-central-1.amazonaws.com/private-catalog-postprocess:${CI_COMMIT_BRANCH}


test:
  image:
    name: amazon/aws-cli
    entrypoint: [""]
  stage: test
  script:
    - aws lambda invoke --function-name ${CI_COMMIT_BRANCH}_post_importer output.json || exit 1
    - cat output.json


