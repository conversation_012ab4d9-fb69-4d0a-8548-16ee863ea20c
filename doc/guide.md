# Guide to Using the "Windsurf" Coding Agent

Welcome to the guide for the "windsurf" coding agent! This agent is designed to help you process data about specific products into a standardized format and save it into files and a database. All interactions with "windsurf" are done through conversation.
 
## Create Windsurf account
Create an account at [windsurf.com](www.windsurf.com)
Download the JetBrains Windsurg plugin or use the Windsurf IDE directly

## How "Windsurf" Works

"windsurf" follows a specific workflow to process product data. It uses a set of scripts located in the `src/catalog_processor_bot` directory. You will guide "windsurf" by telling it what to do at each step. "windsurf" will then execute the necessary commands.

## Adding a New Product to the Catalog: Step-by-Step

Here's how you can use "windsurf" to add a new product:

### 1. Specify the Product Category

*   **Your Action**: Start by telling "windsurf" which product category you want to process. For example, you might say, "I want to process electronics."
*   **"windsurf's" Action**: "windsurf" will then search its database (`data/product_types.csv`) for product types that match the category you provided. This is done using a script similar to:
    ```python
    python -c "import pandas as pd; df = pd.read_csv('data/product_types.csv'); matches = df[df['product_type'].str.contains('cat_string_1', case=False, na=False) & df['product_type'].str.contains('cat_string_2', case=False, na=False)]; print(matches[['product_type']].to_string(index=False))"
    ```

### 2. Confirm Product Types

*   **"windsurf's" Action**: If "windsurf" finds matching product types, it will present them to you as a list.
*   **Your Action**: Review the list and confirm which product types are correct for your data. You might say, "Yes, 'Digital Cameras' and 'Smartphones' are the correct types."

### 3. Initial Data Preprocessing

*   **"windsurf's" Action**: Once you've confirmed the product types, "windsurf" will run a script to perform initial preprocessing on the data for these categories. It will inform you when this step is complete. This script internally uses a command similar to:
    ```bash
    run python -m src.catalog_processor_bot.initial_preprocessing --category "confirmed_category_1" "confirmed_category_2"
    ```

### 4. Data Analysis (Optional)

*   **"windsurf's" Action**: "windsurf" will ask if you want to see an analysis of the processed data.
*   **Your Action**: If you want the analysis, simply say "Yes."
*   **"windsurf's" Action**: "windsurf" will then run an analysis script
    ```bash
    run python -m src.catalog_processor_bot.csv_analyzer "path/to/generated/csv/file.csv"
    ```
     and save the analysis results to a markdown file in the `data` folder. It will then offer to open this markdown file for you to review.

### 5. Custom Data Transformations

When the standard data cleaning and preprocessing steps aren't sufficient, you can apply custom transformations to your CSV data. This process is interactive and iterative:

1. **Transformation Script**
   - The script [src/custom_transformation.py](cci:7://file:///home/<USER>/projects/CatalogProcessorBot/src/custom_transformation.py:0:0-0:0) is used to apply your custom transformations
   - It accepts a single input CSV file as an argument
   - The script is always run on the original raw CSV file to ensure consistency
   - The transformed data is saved with a "cleaned_" prefix in the same directory as the input file

2. **Providing Instructions**
   - Clearly describe the transformations you want to apply (e.g., "Rename column X to Y", "Drop columns A and B", "Convert column C to lowercase")
   - You can provide one instruction at a time or multiple at once

3. **Code Generation**
   - The code will be written to the [perform_custom_transformation()](cci:1://file:///home/<USER>/projects/CatalogProcessorBot/src/custom_transformation.py:6:0-21:13) function in [src/custom_transformation.py](cci:7://file:///home/<USER>/projects/CatalogProcessorBot/src/custom_transformation.py:0:0-0:0)
   - Each new instruction will be appended to the existing code

4. **Preview and Iterate**
   - After each set of instructions, you can review the generated code
   - If changes are needed, simply provide additional instructions
   - The code will be updated incrementally based on your feedback

5. **Execution**
   - When you're satisfied with the transformations, the script will be executed with the command:
     ```bash
     python src/custom_transformation.py --input-csv path/to/original.csv
     ```
   - The transformed data will be saved as `cleaned_original.csv` in the same directory

### 6. Saving to the Products Database

*   **Your Action**: If you are satisfied with the processed data (cleaned or raw), tell "windsurf" that you want to save it to the database.
*   **"windsurf's" Action**: "windsurf" will ask you for the name of the CSV file to be saved and the desired name for the products table in the database. Use small letters with underscores. For example 'tennis_rackets'.
*   **"windsurf's" Action**: "windsurf" will then run a script to save the data, similar to:
    ```bash
    python src/catalog_processor_bot/save_to_products_db.py --product-table-name "table_name" --domain-name "domain_name" "path/to/csv/file.csv"
    ```
    When this script runs, it performs several key operations:
    *   It loads the specified CSV file into a pandas DataFrame.
    *   The script saves the main product data into a database table with _product suffix.
    *   It also saves the processed descriptions (including their embeddings) into a separate table with _descriptions suffix.
    *   Additionally, it embeds unique strings from the database, extracts byte representations and saves them into a separate table with _embeddings_binary suffix.
    *   A BM25 index is created in a separate directory. You then need to copy the directory to the AI core repository
    *   Finally, it saves a configuration YAML file, which stores and settings related to the product. Copy the content of this file to the AI core repository


## General Interaction Rules

*   **Conversation is Key**: All instructions to "windsurf" are given through natural conversation.
*   **Script Execution**: "windsurf" is only permitted to run scripts located in the `src/catalog_processor_bot` directory using the `run python` command (or `python` directly for `save_to_products_db.py`). It cannot change any other files, except for appending to `src/custom_transformation.py` as described above.

By following these steps and interacting clearly with "windsurf," you can efficiently process and manage your product catalog data.
